<?xml version="1.0" encoding="UTF-8"?>
<IO>

<!-- 该节点的名称不可修改,固定为: LOG -->
<TITLE LOG_TYPE="BCP01日志" LOG_RECORD_SIZE="143" >LOG</TITLE> 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="009" BYTE="04" BIT="0" WIDGET_NAME="条目序号" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="150" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="3" READ_OFFSET="013" BYTE="08" BIT="0" WIDGET_NAME="时间" READ_SCRIPT="通用_时间_年月日时分秒.js"/>                               
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="020" BYTE="01" BIT="0" WIDGET_NAME="事件码" READ_SCRIPT="PVPB02_事件解析.js" />           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="020" BYTE="02" BIT="0" WIDGET_NAME="故障码" READ_SCRIPT="PVPB02_故障名称.js" />           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="034" BYTE="01" BIT="0" WIDGET_NAME="逆变模式" READ_SCRIPT="PVPB02_系统状态.js" />         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="027" BYTE="02" BIT="0" WIDGET_NAME="输入频率" READ_SCRIPT="通用_除10_小数1.js" />         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="065" BYTE="02" BIT="0" WIDGET_NAME="母线电压-pfc" />                       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="067" BYTE="02" BIT="0" WIDGET_NAME="R相输入电压" />                        
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="069" BYTE="02" BIT="0" WIDGET_NAME="S相输入电压" />                        
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="071" BYTE="02" BIT="0" WIDGET_NAME="T相输入电压" />                        
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="073" BYTE="02" BIT="0" WIDGET_NAME="R相输入电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="075" BYTE="02" BIT="0" WIDGET_NAME="S相输入电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="077" BYTE="02" BIT="0" WIDGET_NAME="T相输入电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="029" BYTE="02" BIT="0" WIDGET_NAME="输出频率" READ_SCRIPT="通用_除10_小数1.js" />	       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="043" BYTE="02" BIT="0" WIDGET_NAME="母线电压-inv" READ_SCRIPT="通用_除10_小数1.js" />     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="045" BYTE="02" BIT="0" WIDGET_NAME="U相输出电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="047" BYTE="02" BIT="0" WIDGET_NAME="V相输出电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="049" BYTE="02" BIT="0" WIDGET_NAME="W相输出电流" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="051" BYTE="02" BIT="0" WIDGET_NAME="输出电压" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="032" BYTE="01" BIT="0" WIDGET_NAME="逆变状态码" READ_SCRIPT="PVPB02_电机状态.js" />       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="033" BYTE="01" BIT="0" WIDGET_NAME="PFC状态码" READ_SCRIPT="PVPB02_PFC状态.js" />         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="031" BYTE="01" BIT="0" WIDGET_NAME="系统状态码" READ_SCRIPT="PVPB02_系统状态.js" />       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="035" BYTE="01" BIT="0" WIDGET_NAME="PFC模式" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="097" BYTE="04" BIT="0" WIDGET_NAME="累计能耗" READ_SCRIPT="通用_除1000_小数3.js" />       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="061" BYTE="02" BIT="0" WIDGET_NAME="igbt温度" READ_SCRIPT="通用_除10_小数1.js" />         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="063" BYTE="02" BIT="0" WIDGET_NAME="逆变CPU温度" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="079" BYTE="02" BIT="0" WIDGET_NAME="PFC电感1温度" READ_SCRIPT="通用_除10_小数1.js" />     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="081" BYTE="02" BIT="0" WIDGET_NAME="共模电感温度" READ_SCRIPT="通用_除10_小数1.js" />     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="083" BYTE="02" BIT="0" WIDGET_NAME="PFC散热器温度" READ_SCRIPT="通用_除10_小数1.js" />    
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="085" BYTE="02" BIT="0" WIDGET_NAME="PFC-CPU温度" READ_SCRIPT="通用_除10_小数1.js" />      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="036" BYTE="01" BIT="0" WIDGET_NAME="逆变输出频率暂存" />                   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="0" WIDGET_NAME="PFC启动" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="1" WIDGET_NAME="逆变启动" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="2" WIDGET_NAME="逆变使能" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="3" WIDGET_NAME="逆变清除" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="4" WIDGET_NAME="输出O1" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="5" WIDGET_NAME="输出O2" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="6" WIDGET_NAME="输出O3" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="7" WIDGET_NAME="KMON1" />                              
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="042" BYTE="00" BIT="0" WIDGET_NAME="KMON2" />                              
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="042" BYTE="00" BIT="1" WIDGET_NAME="PFC使能" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="042" BYTE="00" BIT="2" WIDGET_NAME="PFC清除" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="042" BYTE="00" BIT="3" WIDGET_NAME="降载运行" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="053" BYTE="02" BIT="0" WIDGET_NAME="0-10v采样" READ_SCRIPT="通用_除10_小数1.js" />        
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="055" BYTE="02" BIT="0" WIDGET_NAME="4-20mA采样" READ_SCRIPT="通用_除10_小数1.js" />       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="057" BYTE="02" BIT="0" WIDGET_NAME="12V采样" READ_SCRIPT="通用_除100_小数2.js" />         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="059" BYTE="02" BIT="0" WIDGET_NAME="5V采样" READ_SCRIPT="通用_除100_小数2.js" />          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="0" WIDGET_NAME="inv_io1" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="1" WIDGET_NAME="inv_io2" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="2" WIDGET_NAME="inv_io3" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="3" WIDGET_NAME="inv_io4" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="4" WIDGET_NAME="inv_io5" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="5" WIDGET_NAME="inv_i1" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="6" WIDGET_NAME="inv_i2" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="087" BYTE="00" BIT="7" WIDGET_NAME="inv_i3" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="0" WIDGET_NAME="inv_i4" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="1" WIDGET_NAME="inv_addr1" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="2" WIDGET_NAME="inv_addr2" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="3" WIDGET_NAME="inv_s1" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="4" WIDGET_NAME="inv_s2" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="5" WIDGET_NAME="inv_hdd1" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="6" WIDGET_NAME="inv_hdd2" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="088" BYTE="00" BIT="7" WIDGET_NAME="inv_hdd3" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="0" WIDGET_NAME="inv_fs_fb" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="1" WIDGET_NAME="inv_fan1_f" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="2" WIDGET_NAME="inv_fan2_f" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="3" WIDGET_NAME="inv_ipm_f" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="4" WIDGET_NAME="inv_ocp_n" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="5" WIDGET_NAME="inv_ocp_p" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="6" WIDGET_NAME="inv_igbt_f" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="089" BYTE="00" BIT="7" WIDGET_NAME="inv_fs" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="0" WIDGET_NAME="inv_clr" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="1" WIDGET_NAME="inv_o1" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="2" WIDGET_NAME="inv_o2" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="3" WIDGET_NAME="inv_o3" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="4" WIDGET_NAME="inv_o1_f" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="5" WIDGET_NAME="inv_o2_f" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="090" BYTE="00" BIT="6" WIDGET_NAME="inv_o3_f" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="0" WIDGET_NAME="pfc_io1" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="1" WIDGET_NAME="pfc_io2" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="2" WIDGET_NAME="pfc_io3" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="3" WIDGET_NAME="pfc_io4" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="4" WIDGET_NAME="pfc_io5" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="5" WIDGET_NAME="pfc_kmon1" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="6" WIDGET_NAME="pfc_kmon1" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="091" BYTE="00" BIT="7" WIDGET_NAME="pfc_clr" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="0" WIDGET_NAME="pfc_fs" />                             
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="1" WIDGET_NAME="pfc_fs_fb_fs" />                       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="2" WIDGET_NAME="pfc_ipm" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="3" WIDGET_NAME="pfc_icp_n" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="4" WIDGET_NAME="pfc_icp_p" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="092" BYTE="00" BIT="5" WIDGET_NAME="pfc_ovp_pbus" />                       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="0" WIDGET_NAME="hard_stop" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="1" WIDGET_NAME="soft_stop" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="2" WIDGET_NAME="diag_lvl_1" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="3" WIDGET_NAME="diag_lvl_2" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="4" WIDGET_NAME="diag_lvl_3" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="5" WIDGET_NAME="diag_soft" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="6" WIDGET_NAME="diag_hard" />                          
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="093" BYTE="00" BIT="7" WIDGET_NAME="diag_pfc_lvl_1" />                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="0" WIDGET_NAME="diag_pfc_lvl_2" />                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="1" WIDGET_NAME="diag_pfc_lvl_3" />                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="2" WIDGET_NAME="diag_pfc_soft" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="3" WIDGET_NAME="diag_pfc_hard" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="4" WIDGET_NAME="com_485" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="5" WIDGET_NAME="com_can" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="6" WIDGET_NAME="com_ptu" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="094" BYTE="00" BIT="7" WIDGET_NAME="com_pfc" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="0" WIDGET_NAME="com_wifi" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="1" WIDGET_NAME="nvs_datas_init" />                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="2" WIDGET_NAME="mount_fs" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="3" WIDGET_NAME="cmd_start_pfc" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="4" WIDGET_NAME="cmd_start_vfd" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="5" WIDGET_NAME="pfc_wdg_timeout" />                    
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="6" WIDGET_NAME="io_init" />                            
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="095" BYTE="00" BIT="7" WIDGET_NAME="rtc_init" />                           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="096" BYTE="01" BIT="0" WIDGET_NAME="风扇占空比" />                         
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="101" BYTE="01" BIT="0" WIDGET_NAME="故障停机次数" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="037" BYTE="02" BIT="0" WIDGET_NAME="母线电压参考值" />                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="039" BYTE="02" BIT="0" WIDGET_NAME="pfc电流参考值" />                      
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="023" BYTE="02" BIT="0" WIDGET_NAME="逆变模块软件版本号" />                 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" EXPORT_XLS="1" WIDGET_DISP="1" READ_OFFSET="025" BYTE="02" BIT="0" WIDGET_NAME="PFC模块软件版本号" />                  

</IO>
