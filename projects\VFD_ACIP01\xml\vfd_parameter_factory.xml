<?xml version="1.0" encoding="UTF-8" ?>
<IO>

<!-- -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="出厂序列号" >PVPB_PARAMTER_FACTORY</TITLE>                                                                                      	
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="32" BIT="0" WIDGET_NAME="整机序列号"		/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="041" WRITE_OFFSET="041" BYTE="32" BIT="0" WIDGET_NAME="控制板" 			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="073" WRITE_OFFSET="073" BYTE="32" BIT="0" WIDGET_NAME="功率主板1" 		/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="105" WRITE_OFFSET="105" BYTE="32" BIT="0" WIDGET_NAME="功率主板2" 		/>					 																								  
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="137" WRITE_OFFSET="137" BYTE="32" BIT="0" WIDGET_NAME="电源板"			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="169" WRITE_OFFSET="169" BYTE="32" BIT="0" WIDGET_NAME="LLC驱动板" 		/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="201" WRITE_OFFSET="201" BYTE="32" BIT="0" WIDGET_NAME="桥接板" 			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="1" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="233" WRITE_OFFSET="233" BYTE="32" BIT="0" WIDGET_NAME="电容板1" 			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="265" WRITE_OFFSET="265" BYTE="32" BIT="0" WIDGET_NAME="电容板2" 			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="297" WRITE_OFFSET="297" BYTE="32" BIT="0" WIDGET_NAME="电容板3" 			/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="329" WRITE_OFFSET="329" BYTE="32" BIT="0" WIDGET_NAME="谐振电容板" 		/>
    <CHILD WIDGET_TYPE="PARAM" WIDGET_ROW="2" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" DO_CRC="0" WIDGET_DISP="5" WIDGET_SHOW="1" WIDGET_EDIT="1" READ_OFFSET="361" WRITE_OFFSET="361" BYTE="32" BIT="0" WIDGET_NAME="电感板1" 			/>					 					

<!-- 硬件版本 -->
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="出厂版本" >PVPB_DEVICE_05</TITLE>																		 		 		  				  					      	 
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="416" WRITE_OFFSET="416" BYTE="2" BIT="0" WIDGET_NAME="整机序列号"	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="418" WRITE_OFFSET="418" BYTE="2" BIT="0" WIDGET_NAME="功率主板1" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="420" WRITE_OFFSET="420" BYTE="2" BIT="0" WIDGET_NAME="功率主板2" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="422" WRITE_OFFSET="422" BYTE="2" BIT="0" WIDGET_NAME="电源板" 		 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="424" WRITE_OFFSET="424" BYTE="2" BIT="0" WIDGET_NAME="控制板"			READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="426" WRITE_OFFSET="426" BYTE="2" BIT="0" WIDGET_NAME="LLC驱动板" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="428" WRITE_OFFSET="428" BYTE="2" BIT="0" WIDGET_NAME="桥接板" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="430" WRITE_OFFSET="430" BYTE="2" BIT="0" WIDGET_NAME="电容板1" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="432" WRITE_OFFSET="432" BYTE="2" BIT="0" WIDGET_NAME="电容板2" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="434" WRITE_OFFSET="434" BYTE="2" BIT="0" WIDGET_NAME="电容板3" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="436" WRITE_OFFSET="436" BYTE="2" BIT="0" WIDGET_NAME="谐振电容板"		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="438" WRITE_OFFSET="438" BYTE="2" BIT="0" WIDGET_NAME="电感板1" 	 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>


</IO>
