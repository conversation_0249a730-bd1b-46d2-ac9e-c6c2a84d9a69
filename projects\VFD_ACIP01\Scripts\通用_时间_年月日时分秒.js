function pad_start(str,targetLength, padString) 
{ 
    while (str.length < targetLength) {
        str = padString + str;
    }
    return str;
}

// 参数传入8byte的hex ,其中 右边6byte为有效数据
// 例:"0x3001080000010100" 有效数据为: 80000010100  -> 08秒 00分 00时 01日 01月 (00+2000)年
function fun(val)
{
		// 取有效数据(右边6byte为有效数据)
		var pyload = val.substr(-12);
	
		var second 	= parseInt(pyload.substring(0, 2), 16);
		var minute 	= parseInt(pyload.substring(2, 4), 16);
		var hour 		= parseInt(pyload.substring(4, 6), 16);
		var day 		=	parseInt(pyload.substring(6, 8), 16);
		var month 	= parseInt(pyload.substring(8, 10), 16);
		var year 		= parseInt(pyload.substring(10, 12), 16) + 2000;
		
		// 固定2位宽 左边补0
		second 	= pad_start(second.toString(),2,'0');
		minute 	= pad_start(minute.toString(),2,'0');
		hour		= pad_start(hour.toString(),2,'0');
		
		day 		= pad_start(day.toString(),2,'0');
		month 	= pad_start(month.toString(),2,'0');
		year 		= pad_start(year.toString(),2,'0');
	
		var time_string = year +  '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second + "\t";
		return time_string;
}


