<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 该节点的名称不可修改,固定为: VFD_FAULT -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="0" COLUMN_STRETCH="0" GROUP_NAME="故障" >VFD_FAULT</TITLE> 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="04" BIT="0" WIDGET_NAME="条目序号" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="150" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="013" BYTE="08" BIT="0" WIDGET_NAME="时间"   READ_SCRIPT="通用_时间_年月日时分秒.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="01" BIT="0" WIDGET_NAME="事件码" READ_SCRIPT="通用_日志事件码解析.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="02" BIT="0" WIDGET_NAME="故障码" READ_SCRIPT="通用_日志诊断码解析.js" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="023" BYTE="01" BIT="0" WIDGET_NAME="设备ID"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="024" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 	READ_SCRIPT="通用_秒转分.js" />	                                                                                                  						  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="033" BYTE="01" BIT="0" WIDGET_NAME="系统状态" 		READ_SCRIPT="通用_状态机.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="01" BIT="0" WIDGET_NAME="LLC状态" 			READ_SCRIPT="通用_状态机.js"/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="082" BYTE="02" BIT="0" WIDGET_NAME="输入电压" 		 READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="082" BYTE="02" BIT="0" WIDGET_NAME="输入预充电电压"  	 READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="088" BYTE="02" BIT="0" WIDGET_NAME="LLC输出电压"      READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="074" BYTE="02" BIT="0" WIDGET_NAME="输入电流" 		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="096" BYTE="02" BIT="0" WIDGET_NAME="输出电流"	READ_SCRIPT="通用_除10_小数1.js"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="041" BYTE="00" BIT="0" WIDGET_NAME="Start" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="02" BIT="0" WIDGET_NAME="LLC输出频率" 	 />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="02" BIT="0" WIDGET_NAME="LLC目标电压" 	 />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="064" BYTE="02" BIT="0" WIDGET_NAME="MCU温度(℃)" 	    READ_SCRIPT="通用_除10_小数1.js" />   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="066" BYTE="02" BIT="0" WIDGET_NAME="3.3V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>						  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="068" BYTE="02" BIT="0" WIDGET_NAME="5V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="070" BYTE="02" BIT="0" WIDGET_NAME="12V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>                                                                                                     

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="055" BYTE="08" BIT="0" WIDGET_NAME="系统故障字" 		READ_SCRIPT="通用_故障字.js"/>                                   	                                                                             

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="090" BYTE="02" BIT="0" WIDGET_NAME="LLC上电压" READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="092" BYTE="02" BIT="0" WIDGET_NAME="LLC中电压" READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="094" BYTE="02" BIT="0" WIDGET_NAME="LLC下电压" READ_SCRIPT="通用_除10_小数1.js"/>


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="106" BYTE="01" BIT="0" WIDGET_NAME="腔内温度(℃)" 	/>   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="107" BYTE="01" BIT="0" WIDGET_NAME="腔内湿度(%)"	/>   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="108" BYTE="01" BIT="0" WIDGET_NAME="输出母线电容(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="109" BYTE="01" BIT="0" WIDGET_NAME="输入母线电容(℃)"    	/>								  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="110" BYTE="01" BIT="0" WIDGET_NAME="LLC1&2模块温度(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="111" BYTE="01" BIT="0" WIDGET_NAME="LLC2&3模块温度(℃)"    	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="112" BYTE="01" BIT="0" WIDGET_NAME="LLC谐振电感温度(℃)"	   	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="113" BYTE="01" BIT="0" WIDGET_NAME="LLC变压器温度(℃)"   	/>								  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="114" BYTE="01" BIT="0" WIDGET_NAME="LLC1&2二极管温度(℃)"   	/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="115" BYTE="01" BIT="0" WIDGET_NAME="LLC2&3二极管温度(℃)"   	/> 						               											               
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="116" BYTE="01" BIT="0" WIDGET_NAME="LLC谐振电容温度(℃)"   	/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="0" WIDGET_NAME="输入1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="1" WIDGET_NAME="输入2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="2" WIDGET_NAME="输入3" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="3" WIDGET_NAME="输入4" />													                                                   
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="4" WIDGET_NAME="输出1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="5" WIDGET_NAME="输出2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="6" WIDGET_NAME="输出3" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="045" BYTE="00" BIT="7" WIDGET_NAME="地址码1" />								  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="046" BYTE="00" BIT="0" WIDGET_NAME="地址码2" />						               											                                                       

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="046" BYTE="00" BIT="5" WIDGET_NAME="F-IPM-LLC"/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="1" WIDGET_NAME="LLC_OCV_UPV"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="2" WIDGET_NAME="LLC_OCV_MIV"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="3" WIDGET_NAME="LLC_OCV_DNV"/>	                                                                                                        
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="4" WIDGET_NAME="F-HD 故障刹车"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="5" WIDGET_NAME="KMON1主继电器" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="00" BIT="6" WIDGET_NAME="KMON2预充电继电器" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="048" BYTE="00" BIT="0" WIDGET_NAME="FS-DR 使能输出"/>	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="048" BYTE="00" BIT="1" WIDGET_NAME="CLR-HD 故障清除"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="048" BYTE="00" BIT="2" WIDGET_NAME="OVP_P_VBout"/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="048" BYTE="00" BIT="4" WIDGET_NAME="FS-DR 使能反馈"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="048" BYTE="00" BIT="5" WIDGET_NAME="POW"/>								               											                                                     
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="052" BYTE="00" BIT="2" WIDGET_NAME="Flash挂载"/>								  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="051" BYTE="00" BIT="5" WIDGET_NAME="CAN通讯" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="051" BYTE="00" BIT="4" WIDGET_NAME="485通讯" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="050" BYTE="00" BIT="2" WIDGET_NAME="diag_lvl_1 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="050" BYTE="00" BIT="3" WIDGET_NAME="diag_lvl_2 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="050" BYTE="00" BIT="4" WIDGET_NAME="diag_lvl_3 " />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="028" BYTE="02" BIT="0" WIDGET_NAME="软件版本" 			READ_SCRIPT="通用_主版本.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="028" BYTE="02" BIT="0" WIDGET_NAME="修订" 				READ_SCRIPT="通用_fix.js" />																								           
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="030" BYTE="02" BIT="0" WIDGET_NAME="硬件版本" 			READ_SCRIPT="通用_主版本.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="032" BYTE="01" BIT="0" WIDGET_NAME="型号" 				READ_SCRIPT="通用_派生型号.js"/>


</IO>                               
















