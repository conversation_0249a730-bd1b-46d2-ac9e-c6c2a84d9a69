<?xml version="1.0" encoding="UTF-8"?>
<IO>

<!--  -->
<DEVICE_ID>17</DEVICE_ID>
<DEVICE_TITLE>蒸发风机变频器</DEVICE_TITLE>     

<!--系统状态   OPERATING_TEXT     -->	                                                                                                                                                  																						           
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="" >DCAC3_INFO</TITLE>     
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="095" BYTE="02" BIT="0" POINT_X="005" POINT_Y="065" WIDGET_NAME="\n输出电压(V)"  		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="097" BYTE="04" BIT="0" POINT_X="005" POINT_Y="110" WIDGET_NAME="\n输出容量(kVA)"  		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="077" BYTE="02" BIT="0" POINT_X="005" POINT_Y="155" WIDGET_NAME="\n输出母线电容(℃)" 	READ_SCRIPT="通用_除10_小数0.js"/>

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="055" BYTE="02" BIT="0" POINT_X="107" POINT_Y="065" WIDGET_NAME="\n输入R相电压(V)"  READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="057" BYTE="02" BIT="0" POINT_X="107" POINT_Y="110" WIDGET_NAME="\n输入S相电压(V)"  READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="065" BYTE="02" BIT="0" POINT_X="107" POINT_Y="155" WIDGET_NAME="\n输入T相电压(V)"  READ_SCRIPT="通用_除10_小数1.js"/>

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="061" BYTE="02" BIT="0" POINT_X="209" POINT_Y="065" WIDGET_NAME="\n输出U相电流(A)"  READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="063" BYTE="02" BIT="0" POINT_X="209" POINT_Y="110" WIDGET_NAME="\n输出V相电流(A)"  READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="125" BYTE="02" BIT="0" POINT_X="209" POINT_Y="155" WIDGET_NAME="\n输出W相电流(A)"  READ_SCRIPT="通用_除10_小数1.js"/>

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="075" BYTE="02" BIT="0" POINT_X="413" POINT_Y="155" WIDGET_NAME="\nIGBT模块(℃)" 		 READ_SCRIPT="通用_除10_小数0.js"/>

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="009" BYTE="01" BIT="0" POINT_X="311" POINT_Y="065" WIDGET_NAME="\n压缩机变频器2状态" READ_SCRIPT="通用_状态机.js" KEEP_MS="2000"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="049" BYTE="00" BIT="6" POINT_X="311" POINT_Y="110" WIDGET_NAME="\n压缩机变频器2通讯" READ_SCRIPT="ACIP01_通讯状态机.js" KEEP_MS="2000"/> 
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="012" BYTE="01" BIT="0" POINT_X="311" POINT_Y="155" WIDGET_NAME="\n压缩机变频器2地址" READ_SCRIPT="通用_HEX.js"/>


</IO>