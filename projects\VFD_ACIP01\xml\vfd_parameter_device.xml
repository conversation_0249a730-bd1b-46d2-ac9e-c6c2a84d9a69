<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 参数设置 -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="参数设置" >PVPB_DEVICE_01</TITLE>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="4" BIT="0" WIDGET_NAME="设置时间" READ_SCRIPT="通用_时间戳_秒.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="013" WRITE_OFFSET="013" BYTE="1" BIT="0" WIDGET_NAME="通信ID设置" />

    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="024" WRITE_OFFSET="024" BYTE="2" BIT="0" WIDGET_NAME="下垂阻值Rd" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="014" WRITE_OFFSET="014" BYTE="4" BIT="0" WIDGET_NAME="RS485波特率(bps)" READ_SCRIPT="通用_解析_波特率.js" WRITE_SCRIPT="通用_设置_波特率.js" ENUM_SCRIPT="通用_枚举_波特率.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="018" WRITE_OFFSET="018" BYTE="1" BIT="0" WIDGET_NAME="RS485校验位" READ_SCRIPT="通用_解析_奇偶校验.js" WRITE_SCRIPT="通用_设置_奇偶校验.js" ENUM_SCRIPT="通用_枚举_奇偶校验.js"/>																						 		 		  				  					      	 
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="019" WRITE_OFFSET="019" BYTE="1" BIT="0" WIDGET_NAME="RS485停止位" 				READ_SCRIPT="通用_解析_停止位.js" WRITE_SCRIPT="通用_设置_停止位.js" 	ENUM_SCRIPT="通用_枚举_停止位.js"/>

    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="2" READ_OFFSET="020" WRITE_OFFSET="020" BYTE="4" BIT="0" WIDGET_NAME="Can波特率(bps)" 
READ_SCRIPT="通用_解析_CAN波特率.js" WRITE_SCRIPT="通用_设置_CAN波特率.js" ENUM_SCRIPT="通用_枚举_CAN波特率.js"/>

<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="控制参数" >PVPB_DEVICE_02</TITLE>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="028" WRITE_OFFSET="028" BYTE="4" BIT="0" WIDGET_NAME="电压环hKp_Gain" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="032" WRITE_OFFSET="032" BYTE="4" BIT="0" WIDGET_NAME="电压环hKp_Divisor" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="036" WRITE_OFFSET="036" BYTE="4" BIT="0" WIDGET_NAME="电压环hKi_Gain" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="040" WRITE_OFFSET="040" BYTE="4" BIT="0" WIDGET_NAME="电压环hKi_Divisor" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="044" WRITE_OFFSET="044" BYTE="4" BIT="0" WIDGET_NAME="电压环hLower_Limit_Output" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="048" WRITE_OFFSET="048" BYTE="4" BIT="0" WIDGET_NAME="电压环hUpper_Limit_Output" />
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="052" WRITE_OFFSET="052" BYTE="4" BIT="0" WIDGET_NAME="电压环hLower_Limit_Integral" />
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="056" WRITE_OFFSET="056" BYTE="4" BIT="0" WIDGET_NAME="电压环hUpper_Limit_Integral" />

    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="060" WRITE_OFFSET="060" BYTE="4" BIT="0" WIDGET_NAME="电流环hKp_Gain" />
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="064" WRITE_OFFSET="064" BYTE="4" BIT="0" WIDGET_NAME="电流环hKp_Divisor" />
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="068" WRITE_OFFSET="068" BYTE="4" BIT="0" WIDGET_NAME="电流环hKi_Gain" />
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="072" WRITE_OFFSET="072" BYTE="4" BIT="0" WIDGET_NAME="电流环hKi_Divisor" />
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="076" WRITE_OFFSET="076" BYTE="4" BIT="0" WIDGET_NAME="电流环hLower_Limit_Output" />
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="080" WRITE_OFFSET="080" BYTE="4" BIT="0" WIDGET_NAME="电流环hUpper_Limit_Output" />
    <CHILD WIDGET_ROW="5" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="084" WRITE_OFFSET="084" BYTE="4" BIT="0" WIDGET_NAME="电流环hLower_Limit_Integral" />
    <CHILD WIDGET_ROW="5" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="088" WRITE_OFFSET="088" BYTE="4" BIT="0" WIDGET_NAME="电流环hUpper_Limit_Integral" />


    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="092" WRITE_OFFSET="092" BYTE="2" BIT="0" WIDGET_NAME="llc.IDropCnt" />
    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="094" WRITE_OFFSET="094" BYTE="2" BIT="0" WIDGET_NAME="llc.IDropCoff" />
    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" 		WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="096" WRITE_OFFSET="096" BYTE="2" BIT="0" WIDGET_NAME="llc.UDropCoff" />
</IO>
