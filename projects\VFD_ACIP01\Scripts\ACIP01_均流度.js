// 精确计算float相加
function accAdd(arg1,arg2){
  var r1,r2,m;
  try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
  try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
  m=Math.pow(10,Math.max(r1,r2))
  return (arg1*m+arg2*m)/m
}
// 精确计算float相乘
function accMul(arg1,arg2) {
  var m=0,s1=arg1.toString(),s2=arg2.toString();
  try{m+=s1.split(".")[1].length}catch(e){}
  try{m+=s2.split(".")[1].length}catch(e){}
  return  Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m)
} 

function fun(val)
{
	// 额定总输出电流
	var I1 = 27 + 27;
	
	// 实测的总输出电流
	var str1 = xml_parser.read_data_xml("script_global.xml","ACIP01_DCDC1_OUT_A");
	var str2 = xml_parser.read_data_xml("script_global.xml","ACIP01_DCDC2_OUT_A");
	var DCDC1 = parseFloat(str1);
	var DCDC2 = parseFloat(str2);
	var Sigma_I = accAdd(DCDC1,DCDC2);
	
	// 模块的标称电流
	var I2 = 27;
	
	// 计算均流度
	var uniformity1 = (Sigma_I / I1) - (DCDC1 / I2);  
	var uniformity2 = (Sigma_I / I1) - (DCDC2 / I2);   
	var uniformity =  Math.max(uniformity1,uniformity2);
	
	// 百分比显示, 2位小数
	uniformity = uniformity.toFixed(4);
	uniformity = accMul(uniformity,100);
	uniformity = uniformity.toFixed(2);
	
	// 调试信息
	xml_parser.write_data_xml("script_global.xml","str1",str1);
	xml_parser.write_data_xml("script_global.xml","str2",str2);
	xml_parser.write_data_xml("script_global.xml","DCDC1",DCDC1);
	xml_parser.write_data_xml("script_global.xml","DCDC2",DCDC2);
	xml_parser.write_data_xml("script_global.xml","Sigma_I",Sigma_I);
	xml_parser.write_data_xml("script_global.xml","ACIP01_UNIFORMITY1",uniformity);
	xml_parser.write_data_xml("script_global.xml","ACIP01_UNIFORMITY2",uniformity);
	xml_parser.write_data_xml("script_global.xml","ACIP01_UNIFORMITY",uniformity);
	
	//
	return uniformity;
}
