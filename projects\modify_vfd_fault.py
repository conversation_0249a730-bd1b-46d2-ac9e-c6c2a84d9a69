#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于递归遍历文件夹，修改所有vfd_fault.xml文件中的特定内容
"""

import os
import re
import sys

def modify_vfd_fault_xml(file_path):
    """
    修改vfd_fault.xml文件中的特定内容
    
    修改规则：
    1. 找到 WIDGET_NAME="故障码" 和 READ_SCRIPT="通用_故障字_日志诊断码.js" 的行，
       将 READ_SCRIPT="通用_故障字_日志诊断码.js" 修改为 READ_SCRIPT="通用_故障字.js"
    
    2. 找到 WIDGET_NAME="系统故障字" 和 READ_SCRIPT="通用_日志诊断码解析.js" 的行，
       移除 READ_SCRIPT="通用_日志诊断码解析.js"
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        modified = False
        
        # 规则1: 修改故障码行的READ_SCRIPT
        # 匹配包含 WIDGET_NAME="故障码" 和 READ_SCRIPT="通用_故障字_日志诊断码.js" 的行
        pattern1 = r'(<CHILD[^>]*WIDGET_NAME="故障码"[^>]*READ_SCRIPT=")通用_故障字_日志诊断码\.js(")'
        if re.search(pattern1, content):
            content = re.sub(pattern1, r'\1通用_故障字.js\2', content)
            modified = True
            print(f"  - 修改了故障码的READ_SCRIPT")
        
        # 规则2: 移除系统故障字行的READ_SCRIPT
        # 匹配包含 WIDGET_NAME="系统故障字" 和 READ_SCRIPT="通用_日志诊断码解析.js" 的行
        pattern2 = r'(<CHILD[^>]*WIDGET_NAME="系统故障字"[^>]*)\s*READ_SCRIPT="通用_日志诊断码解析\.js"\s*([^>]*>)'
        if re.search(pattern2, content):
            content = re.sub(pattern2, r'\1\2', content)
            modified = True
            print(f"  - 移除了系统故障字的READ_SCRIPT")
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        else:
            return False
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def find_and_modify_vfd_fault_files(root_dir):
    """
    递归遍历目录，找到所有vfd_fault.xml文件并进行修改
    """
    modified_files = []
    total_files = 0
    
    print(f"开始遍历目录: {root_dir}")
    print("=" * 60)
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file == "vfd_fault.xml":
                file_path = os.path.join(root, file)
                total_files += 1
                
                print(f"处理文件: {file_path}")
                
                if modify_vfd_fault_xml(file_path):
                    modified_files.append(file_path)
                    print(f"  ✓ 文件已修改")
                else:
                    print(f"  - 无需修改")
                print()
    
    print("=" * 60)
    print(f"处理完成!")
    print(f"总共找到 {total_files} 个 vfd_fault.xml 文件")
    print(f"修改了 {len(modified_files)} 个文件")
    
    if modified_files:
        print("\n已修改的文件列表:")
        for file_path in modified_files:
            print(f"  - {file_path}")

def main():
    """主函数"""
    # 指定具体的目录路径
    target_dir = r"D:\项目集合\05_Ptu_Release\Ptu_Release\projects"

    # 如果有命令行参数，使用指定目录
    if len(sys.argv) > 1:
        target_dir = sys.argv[1]

    if not os.path.exists(target_dir):
        print(f"错误: 目录 {target_dir} 不存在")
        sys.exit(1)

    print(f"VFD Fault XML 文件修改工具")
    print(f"目标目录: {target_dir}")
    print()

    find_and_modify_vfd_fault_files(target_dir)

if __name__ == "__main__":
    main()
