<?xml version="1.0" encoding="UTF-8" ?>
<IO>

<!-- 运营统计 -->                                                                                                                                                                  																						 		 		  				  					      	 
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="运营统计" >PVPB_DEVICE_02</TITLE> 
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="073" WRITE_OFFSET="073" BYTE="1" BIT="0" WIDGET_NAME="清除全部统计数据" />
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="4" READ_OFFSET="074" WRITE_OFFSET="074" BYTE="4" BIT="0" WIDGET_NAME="DC750输入总能耗(kWh)" READ_SCRIPT="通用_截取_小数1.js"/>
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="090" WRITE_OFFSET="090" BYTE="4" BIT="0" WIDGET_NAME="运行时间(h)"		  READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="094" WRITE_OFFSET="094" BYTE="4" BIT="0" WIDGET_NAME="DC750V累计工作时间(h)" READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="098" WRITE_OFFSET="098" BYTE="4" BIT="0" WIDGET_NAME="DC750V上电次数" />	                                                                                                                                                                                                   
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="102" WRITE_OFFSET="102" BYTE="4" BIT="0" WIDGET_NAME="DC24V上电次数" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="106" WRITE_OFFSET="106" BYTE="4" BIT="0" WIDGET_NAME="DC750V预充电吸合次数" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="110" WRITE_OFFSET="110" BYTE="4" BIT="0" WIDGET_NAME="DC750V主继电器吸合次数" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="114" WRITE_OFFSET="114" BYTE="4" BIT="0" WIDGET_NAME="DC750V启停次数" />                                                                                                                                                                      
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="118" WRITE_OFFSET="118" BYTE="4" BIT="0" WIDGET_NAME="散热风扇工作时间" READ_SCRIPT="通用_秒转分.js"/>

    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="122" WRITE_OFFSET="122" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容50°以下" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="126" WRITE_OFFSET="126" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容50°-60°"  	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="130" WRITE_OFFSET="130" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容60°-70°"  	READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                                                               
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="134" WRITE_OFFSET="134" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容70°-80°"	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="138" WRITE_OFFSET="138" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容80°-90°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="142" WRITE_OFFSET="142" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容90°-100°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="146" WRITE_OFFSET="146" BYTE="4" BIT="0" WIDGET_NAME="输入母线电容>100°" 		READ_SCRIPT="通用_秒转分.js"/>

    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="150" WRITE_OFFSET="150" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容50°以下" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="154" WRITE_OFFSET="154" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容50°-60°"  	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="158" WRITE_OFFSET="158" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容60°-70°"  	READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                                                               
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="162" WRITE_OFFSET="162" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容70°-80°"	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="166" WRITE_OFFSET="166" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容80°-90°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="170" WRITE_OFFSET="170" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容90°-100°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="174" WRITE_OFFSET="174" BYTE="4" BIT="0" WIDGET_NAME="输出母线电容>100°" 		READ_SCRIPT="通用_秒转分.js"/>

    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="178" WRITE_OFFSET="178" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容50°以下" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="182" WRITE_OFFSET="182" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容50°-60°"  	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="186" WRITE_OFFSET="186" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容60°-70°"  	READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                                                               
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="190" WRITE_OFFSET="190" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容70°-80°"	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="194" WRITE_OFFSET="194" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容80°-90°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="198" WRITE_OFFSET="198" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容90°-100°" 	READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="202" WRITE_OFFSET="202" BYTE="4" BIT="0" WIDGET_NAME="LLC谐振电容>100°" 		READ_SCRIPT="通用_秒转分.js"/>


    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="206" WRITE_OFFSET="206" BYTE="4" BIT="0" WIDGET_NAME="输入功率0-1kW" 	READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                        
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="210" WRITE_OFFSET="210" BYTE="4" BIT="0" WIDGET_NAME="输入功率1-1.5kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="214" WRITE_OFFSET="214" BYTE="4" BIT="0" WIDGET_NAME="输入功率1.5-2kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="218" WRITE_OFFSET="218" BYTE="4" BIT="0" WIDGET_NAME="输入功率2-2.5kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="222" WRITE_OFFSET="222" BYTE="4" BIT="0" WIDGET_NAME="输入功率2.5-3kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                 
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="226" WRITE_OFFSET="226" BYTE="4" BIT="0" WIDGET_NAME="输入功率3-3.5kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="230" WRITE_OFFSET="230" BYTE="4" BIT="0" WIDGET_NAME="输入功率3.5-4kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="234" WRITE_OFFSET="234" BYTE="4" BIT="0" WIDGET_NAME="输入功率4-4.5kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="238" WRITE_OFFSET="238" BYTE="4" BIT="0" WIDGET_NAME="输入功率4.5-5kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                 
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="242" WRITE_OFFSET="242" BYTE="4" BIT="0" WIDGET_NAME="输入功率5-5.5kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="246" WRITE_OFFSET="246" BYTE="4" BIT="0" WIDGET_NAME="输入功率5.5-6kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="250" WRITE_OFFSET="250" BYTE="4" BIT="0" WIDGET_NAME="输入功率6-7kW"   READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="254" WRITE_OFFSET="254" BYTE="4" BIT="0" WIDGET_NAME="输入功率7-1kW"   READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                  
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="258" WRITE_OFFSET="258" BYTE="4" BIT="0" WIDGET_NAME="输入功率8-9kW"   READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="262" WRITE_OFFSET="262" BYTE="4" BIT="0" WIDGET_NAME="输入功率9-10kW"  READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="266" WRITE_OFFSET="266" BYTE="4" BIT="0" WIDGET_NAME="输入功率10-11kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="270" WRITE_OFFSET="270" BYTE="4" BIT="0" WIDGET_NAME="输入功率11-12kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                  
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="274" WRITE_OFFSET="274" BYTE="4" BIT="0" WIDGET_NAME="输入功率12-13kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="278" WRITE_OFFSET="278" BYTE="4" BIT="0" WIDGET_NAME="输入功率13-14kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="282" WRITE_OFFSET="282" BYTE="4" BIT="0" WIDGET_NAME="输入功率14-15kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="286" WRITE_OFFSET="286" BYTE="4" BIT="0" WIDGET_NAME="输入功率15-16kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                     
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="290" WRITE_OFFSET="290" BYTE="4" BIT="0" WIDGET_NAME="输入功率16-17kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="294" WRITE_OFFSET="294" BYTE="4" BIT="0" WIDGET_NAME="输入功率17-18kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="298" WRITE_OFFSET="298" BYTE="4" BIT="0" WIDGET_NAME="输入功率18-19kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="21" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="302" WRITE_OFFSET="302" BYTE="4" BIT="0" WIDGET_NAME="输入功率19-20kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                            
    <CHILD WIDGET_ROW="21" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="306" WRITE_OFFSET="306" BYTE="4" BIT="0" WIDGET_NAME="输入功率20-21kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="21" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="310" WRITE_OFFSET="310" BYTE="4" BIT="0" WIDGET_NAME="输入功率21-22kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="22" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="314" WRITE_OFFSET="314" BYTE="4" BIT="0" WIDGET_NAME="输入功率22-23kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="22" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="318" WRITE_OFFSET="318" BYTE="4" BIT="0" WIDGET_NAME="输入功率23-24kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                                                                
    <CHILD WIDGET_ROW="22" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="322" WRITE_OFFSET="322" BYTE="4" BIT="0" WIDGET_NAME="输入功率24-25kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="23" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="326" WRITE_OFFSET="326" BYTE="4" BIT="0" WIDGET_NAME="输入功率25-26kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="23" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="330" WRITE_OFFSET="330" BYTE="4" BIT="0" WIDGET_NAME="输入功率26-27kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="23" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="334" WRITE_OFFSET="334" BYTE="4" BIT="0" WIDGET_NAME="输入功率27-28kW" READ_SCRIPT="通用_秒转分.js"/>	                                                                                                                                                                                                   
    <CHILD WIDGET_ROW="24" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="338" WRITE_OFFSET="338" BYTE="4" BIT="0" WIDGET_NAME="输入功率28-29kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="24" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="342" WRITE_OFFSET="342" BYTE="4" BIT="0" WIDGET_NAME="输入功率29-30kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="24" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="346" WRITE_OFFSET="346" BYTE="4" BIT="0" WIDGET_NAME="输入功率30-31kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="25" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="350" WRITE_OFFSET="350" BYTE="4" BIT="0" WIDGET_NAME="输入功率31-32kW" READ_SCRIPT="通用_秒转分.js"/>                                                                                                                                                                                                   
    <CHILD WIDGET_ROW="25" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="354" WRITE_OFFSET="354" BYTE="4" BIT="0" WIDGET_NAME="输入功率32-33kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="25" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="358" WRITE_OFFSET="358" BYTE="4" BIT="0" WIDGET_NAME="输入功率33-34kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="26" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="362" WRITE_OFFSET="362" BYTE="4" BIT="0" WIDGET_NAME="输入功率34-35kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="26" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="366" WRITE_OFFSET="366" BYTE="4" BIT="0" WIDGET_NAME="输入功率35-36kW" READ_SCRIPT="通用_秒转分.js"/>	                                                                                                                                                                                                   
    <CHILD WIDGET_ROW="26" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="370" WRITE_OFFSET="370" BYTE="4" BIT="0" WIDGET_NAME="输入功率36-37kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="27" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="374" WRITE_OFFSET="374" BYTE="4" BIT="0" WIDGET_NAME="输入功率37-38kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="27" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="378" WRITE_OFFSET="378" BYTE="4" BIT="0" WIDGET_NAME="输入功率38-39kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="27" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="382" WRITE_OFFSET="382" BYTE="4" BIT="0" WIDGET_NAME="输入功率39-40kW" READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="28" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="386" WRITE_OFFSET="386" BYTE="4" BIT="0" WIDGET_NAME="输入功率40-41kW"  READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="28" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="390" WRITE_OFFSET="390" BYTE="4" BIT="0" WIDGET_NAME="输入功率41-42kW"  READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="28" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="394" WRITE_OFFSET="394" BYTE="4" BIT="0" WIDGET_NAME="输入功率42-43kW"  READ_SCRIPT="通用_秒转分.js"/>
    <CHILD WIDGET_ROW="29" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="398" WRITE_OFFSET="398" BYTE="4" BIT="0" WIDGET_NAME="输入功率43kW以上" READ_SCRIPT="通用_秒转分.js"/>	


<!-- 校准参数 -->
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="校准参数" >PVPB_DEVICE_04</TITLE>																		 		 		  				  					      	 
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="2" BIT="0" WIDGET_NAME="MCU温度(℃)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="011" WRITE_OFFSET="011" BYTE="2" BIT="0" WIDGET_NAME="控制板温度(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="013" WRITE_OFFSET="013" BYTE="2" BIT="0" WIDGET_NAME="输入母线电容(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>	 		 		  				  				          	                                              
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="015" WRITE_OFFSET="015" BYTE="2" BIT="0" WIDGET_NAME="LLC1&2模块(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="017" WRITE_OFFSET="017" BYTE="2" BIT="0" WIDGET_NAME="LLC2&3模块(℃)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="019" WRITE_OFFSET="019" BYTE="2" BIT="0" WIDGET_NAME="LLC变压器(℃)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="021" WRITE_OFFSET="021" BYTE="2" BIT="0" WIDGET_NAME="LLC谐振电感℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="023" WRITE_OFFSET="023" BYTE="2" BIT="0" WIDGET_NAME="LLC谐振电容(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>		
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="025" WRITE_OFFSET="025" BYTE="2" BIT="0" WIDGET_NAME="LLC整流1&2二极管(℃)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="027" WRITE_OFFSET="027" BYTE="2" BIT="0" WIDGET_NAME="LLC整流2&3二极管(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="029" WRITE_OFFSET="029" BYTE="2" BIT="0" WIDGET_NAME="输出母线电容(℃)" 		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>																																													
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="031" WRITE_OFFSET="031" BYTE="2" BIT="0" WIDGET_NAME="12V电压(℃)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>   
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="033" WRITE_OFFSET="033" BYTE="2" BIT="0" WIDGET_NAME="3.3V电压(V)"  			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="035" WRITE_OFFSET="035" BYTE="2" BIT="0" WIDGET_NAME="5V电压(A)"  			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="037" WRITE_OFFSET="037" BYTE="2" BIT="0" WIDGET_NAME="输入电压(A)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>																																											
    <CHILD WIDGET_ROW="5" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="039" WRITE_OFFSET="039" BYTE="2" BIT="0" WIDGET_NAME="输入预充电电压(A)"  		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="5" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="041" WRITE_OFFSET="041" BYTE="2" BIT="0" WIDGET_NAME="LLC上电压(V)"     		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="5" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="043" WRITE_OFFSET="043" BYTE="2" BIT="0" WIDGET_NAME="LLC中电压(V)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="045" WRITE_OFFSET="045" BYTE="2" BIT="0" WIDGET_NAME="LLC下电压(V)"   		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>																																											
    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="047" WRITE_OFFSET="047" BYTE="2" BIT="0" WIDGET_NAME="输出电压(A)"  			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="6" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="049" WRITE_OFFSET="049" BYTE="2" BIT="0" WIDGET_NAME="输入电流(A)"     		READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>
    <CHILD WIDGET_ROW="7" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="051" WRITE_OFFSET="051" BYTE="2" BIT="0" WIDGET_NAME="输出电流(A)" 			READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js"/>


</IO>
