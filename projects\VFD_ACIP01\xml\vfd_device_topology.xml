<?xml version="1.0" encoding="UTF-8"?>
<IO>

<!-- 
自定义数据下发
WRITE_OFFSET为0x81帧的写入偏移,跟上面的回读偏移无关联
--> 
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="2" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="" >PUT_PARAMS</TITLE>
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="13" BYTE="1" BIT="0" WIDGET_NAME="控制方式" 		SWITCH_ON_TEXT="PTU控制" SWITCH_OFF_TEXT="DI控制" WRITE_SCRIPT="VFD_控制方式.js"/>
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="启机" 	 SWITCH_OFF_TEXT="停机" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="24" BYTE="2" BIT="0" WIDGET_NAME="LLC输出电压" 	DEFAULT_VALUE="0" 	 WIDGET_REGEXP="0|[1-9][0-9]*" />


</IO>
