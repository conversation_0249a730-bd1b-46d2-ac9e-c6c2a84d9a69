<?xml version="1.0" encoding="UTF-8"?>
<IO>

<!--  -->
<DEVICE_ID>2</DEVICE_ID>
<DEVICE_TITLE>DCDC单元2</DEVICE_TITLE>     

<!--系统状态   OPERATING_TEXT     -->	                                                                                                                                                  																						             
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="" >DCDC2_INFO</TITLE>     
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="068" BYTE="02" BIT="0" POINT_X="020" POINT_Y="270" WIDGET_NAME="\n输入电压(V)" 		READ_SCRIPT="通用_除10_小数1.js" SHARE_KEY="ACIP01_DCDC2_IN_V"/>  
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="082" BYTE="02" BIT="0" POINT_X="122" POINT_Y="270" WIDGET_NAME="\n输入电流(A)"		READ_SCRIPT="通用_除10_小数1.js" SHARE_KEY="ACIP01_DCDC2_IN_A"/>                     
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="143" BYTE="04" BIT="0" POINT_X="224" POINT_Y="270" WIDGET_NAME="\n输入功率(kW)"		READ_SCRIPT="通用_除1000_小数1.js" SHARE_KEY="ACIP01_DCDC2_IN_W"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="074" BYTE="02" BIT="0" POINT_X="326" POINT_Y="270" WIDGET_NAME="\n输入预充电电压(V)"	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="096" BYTE="02" BIT="0" POINT_X="428" POINT_Y="270" WIDGET_NAME="\n输入母线电容(℃)" 		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="02" BIT="0" POINT_X="530" POINT_Y="270" WIDGET_NAME="\nLLC2&3模块温度(℃)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="102" BYTE="02" BIT="0" POINT_X="632" POINT_Y="270" WIDGET_NAME="\nLLC1变压器(℃)"	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="108" BYTE="02" BIT="0" POINT_X="734" POINT_Y="270" WIDGET_NAME="\nLLC整流二极管(℃)"	READ_SCRIPT="通用_除10_小数1.js" />

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="070" BYTE="02" BIT="0" POINT_X="020" POINT_Y="315" WIDGET_NAME="\n输出电压(V)"		READ_SCRIPT="通用_除10_小数1.js" SHARE_KEY="ACIP01_DCDC2_OUT_V"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="072" BYTE="02" BIT="0" POINT_X="122" POINT_Y="315" WIDGET_NAME="\n输出电流(A)" 		READ_SCRIPT="通用_除10_小数1.js" SHARE_KEY="ACIP01_DCDC2_OUT_A"/> 
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="147" BYTE="04" BIT="0" POINT_X="224" POINT_Y="315" WIDGET_NAME="\n输出功率(kW)" 	READ_SCRIPT="通用_除1000_小数1.js" SHARE_KEY="ACIP01_DCDC2_OUT_W"/>    	
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="030" BYTE="01" BIT="0" POINT_X="326" POINT_Y="315" WIDGET_NAME="\nDCDC单元2状态"  READ_SCRIPT="通用_状态机.js" KEEP_MS="2000"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="030" BYTE="02" BIT="0" POINT_X="428" POINT_Y="315" WIDGET_NAME="\nDCDC单元2通讯"  READ_SCRIPT="ACIP01_通讯状态机.js" KEEP_MS="2000"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="141" BYTE="00" BIT="0" POINT_X="530" POINT_Y="315" WIDGET_NAME="\nDCDC单元2内网通讯" READ_SCRIPT="ACIP01_内网通讯状态机.js" KEEP_MS="2000"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="003" BYTE="01" BIT="0" POINT_X="632" POINT_Y="315" WIDGET_NAME="\nDCDC单元2地址"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="021" BYTE="04" BIT="0" POINT_X="734" POINT_Y="315" WIDGET_NAME="\nDCDC单元2时钟"	READ_SCRIPT="通用_时间戳_秒.js"/>

    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="076" BYTE="02" BIT="0" POINT_X="290" POINT_Y="070" WIDGET_NAME="\nLLC上电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>                      
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="078" BYTE="02" BIT="0" POINT_X="290" POINT_Y="120" WIDGET_NAME="\nLLC中电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="080" BYTE="02" BIT="0" POINT_X="290" POINT_Y="170" WIDGET_NAME="\nLLC下电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>


    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="000" BYTE="02" BIT="0" POINT_X="1664" POINT_Y="1270" WIDGET_NAME="DCDC单元1均流度(100%)" READ_SCRIPT="ACIP01_均流度.js"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="5" POINT_X="1664" POINT_Y="1270" WIDGET_NAME="KMON1-主接触器" SHARE_KEY="ACIP01_DCDC2_KMON1_MAIN"/>
    <CHILD WIDGET_TYPE="TEXT2" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="6" POINT_X="1664" POINT_Y="1270" WIDGET_NAME="KMON2-预充电" SHARE_KEY="ACIP01_DCDC2_KMON2_PRECHARGE"/>

</IO>
