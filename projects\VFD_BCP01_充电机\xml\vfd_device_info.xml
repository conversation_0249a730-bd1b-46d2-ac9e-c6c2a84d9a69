<?xml version="1.0" encoding="UTF-8"?>
<IO>

<!-- RTC在合并数组中的偏移, 各项目可能不同  -->
<RTC READ_OFFSET="213" />

<!--系统状态   OPERATING_TEXT     -->	                                                                                                                                                  																						           
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="系统状态" >CONTROLLER_SYSTEM_INFO</TITLE>                                                           
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="002" COLUMN_WIDTH="060" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="01" BIT="0" WIDGET_NAME="系统状态" 			READ_SCRIPT="通用_系统模式.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="003" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="032" BYTE="01" BIT="0" WIDGET_NAME="PFC状态" 			READ_SCRIPT="通用_PFC升压状态.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="004" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="034" BYTE="01" BIT="0" WIDGET_NAME="输出状态" 			READ_SCRIPT="通用_逆变状态.js"/>	                                                                                                                   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="005" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="035" BYTE="04" BIT="0" WIDGET_NAME="累计上电时间(h)" 	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="006" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="039" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 		READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="007" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="043" BYTE="04" BIT="0" WIDGET_NAME="累计运行时间" 		READ_SCRIPT="通用_秒转分.js" />		                                                                                                  
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="008" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="047" BYTE="02" BIT="0" WIDGET_NAME="CPU温度(℃)"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="009" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="145" BYTE="02" BIT="0" WIDGET_NAME="3.3V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="010" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="049" BYTE="02" BIT="0" WIDGET_NAME="5V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="011" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="073" BYTE="02" BIT="0" WIDGET_NAME="12V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>                                                                                                     
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="012" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="04" BIT="0" WIDGET_NAME="上电累计次数"/>                                                                                                 
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="013" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="153" BYTE="02" BIT="0" WIDGET_NAME="交流额定功率(kW)" 	READ_SCRIPT="通用_除100_小数2.js"/>  
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="014" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="159" BYTE="02" BIT="0" WIDGET_NAME="直流额定功率(kW)" 	READ_SCRIPT="通用_除100_小数2.js"/>  						               											                   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="015" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="147" BYTE="01" BIT="0" WIDGET_NAME="风扇占空比(%)" 	/>

<!-- 实时故障-->
<TITLE GROUP_ROW="0" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="0" GROUP_NAME="实时故障(右击清除记录)" >REALTIME_PFC_FAULT</TITLE>           
    <CHILD WIDGET_TYPE="FAULT" COLUMN_INDEX="016" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="020" BYTE="12" BIT="0" WIDGET_NAME="系统故障字" 		READ_SCRIPT="通用_故障字.js"/>

<!-- PFC信息-->
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="三相输入PFC" >PFC_INFO</TITLE>                                                                                 

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="017" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="075" BYTE="02" BIT="0" WIDGET_NAME="输入R相电压(V)" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="018" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="081" BYTE="02" BIT="0" WIDGET_NAME="输入R相电流(A)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="019" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="087" BYTE="02" BIT="0" WIDGET_NAME="输入频率(Hz)" 		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="020" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="063" BYTE="02" BIT="0" WIDGET_NAME="输入容量(kVA)" 	READ_SCRIPT="通用_除100_小数2.js"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="021" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="077" BYTE="02" BIT="0" WIDGET_NAME="输入S相电压(V)" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="022" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="083" BYTE="02" BIT="0" WIDGET_NAME="输入S相电流(A)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="023" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="101" BYTE="02" BIT="0" WIDGET_NAME="母线电压(V)" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="024" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="057" BYTE="04" BIT="0" WIDGET_NAME="交流当前能耗(kWh)"READ_SCRIPT="通用_除1000_小数3.js"/>			 				  

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="025" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="079" BYTE="02" BIT="0" WIDGET_NAME="输入T相电压(V)"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="026" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="085" BYTE="02" BIT="0" WIDGET_NAME="输入T相电流(A)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="027" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="141" BYTE="02" BIT="0" WIDGET_NAME="预留(℃)" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="028" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="053" BYTE="04" BIT="0" WIDGET_NAME="交流累计能耗(kWh)"READ_SCRIPT="通用_除1000_小数3.js"/>



<!-- 输出调压-->
<TITLE GROUP_ROW="2" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="输出调压" >INVERT_INFO</TITLE>                                                                                   

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="029" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="103" BYTE="02" BIT="0" WIDGET_NAME="输出原边电流(A)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="030" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="109" BYTE="02" BIT="0" WIDGET_NAME="母线电压(V)" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="031" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="111" BYTE="02" BIT="0" WIDGET_NAME="输出频率(kHz)" 	READ_SCRIPT="通用_除100_小数2.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="032" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="125" BYTE="02" BIT="0" WIDGET_NAME="TIM占空比(%)" />		

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="033" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="105" BYTE="02" BIT="0" WIDGET_NAME="前_输出电压(V)" 	READ_SCRIPT="通用_除10_小数1.js"  />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="034" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="091" BYTE="02" BIT="0" WIDGET_NAME="输出电压(V)"  		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="035" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="127" BYTE="02" BIT="0" WIDGET_NAME="目标电压(V)"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="036" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="113" BYTE="04" BIT="0" WIDGET_NAME="累计380V工作次数"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="037" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="065" BYTE="02" BIT="0" WIDGET_NAME="输出功率(kW)" 	    READ_SCRIPT="通用_除100_小数2.js"/> 					               											                                               
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="038" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="071" BYTE="02" BIT="0" WIDGET_NAME="输出总电流(A)" 	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="039" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="097" BYTE="02" BIT="0" WIDGET_NAME="充电电流(A)"		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="040" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="02" BIT="0" WIDGET_NAME="直流输出功率(kW)" 	READ_SCRIPT="通用_除100_小数2.js"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="041" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="133" BYTE="04" BIT="0" WIDGET_NAME="直流当前能耗(kWh)"	READ_SCRIPT="通用_除1000_小数3.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="042" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="129" BYTE="04" BIT="0" WIDGET_NAME="直流累计能耗(kWh)" READ_SCRIPT="通用_除1000_小数3.js"/>

<!-- 温度信息-->
<TITLE GROUP_ROW="3" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="温度检测" >TEMP_INFO</TITLE>   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="043" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="155" BYTE="02" BIT="0" WIDGET_NAME="共模电感温度(℃)"    READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="044" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="143" BYTE="02" BIT="0" WIDGET_NAME="PFC电感温度(℃)"     READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="045" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="089" BYTE="02" BIT="0" WIDGET_NAME="PFC模块温度(℃)"     READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="046" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="093" BYTE="02" BIT="0" WIDGET_NAME="母线电容温度(℃)"    READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="047" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="117" BYTE="02" BIT="0" WIDGET_NAME="DC/DC模块温度(℃)"	  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="048" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="095" BYTE="02" BIT="0" WIDGET_NAME="变压器温度(℃)" 	  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="049" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="099" BYTE="02" BIT="0" WIDGET_NAME="整流二极管温度(℃)"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="050" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="107" BYTE="02" BIT="0" WIDGET_NAME="外接电池温度(℃)" 	  READ_SCRIPT="通用_除10_小数1.js" /> 						               											               

<!-- 数字量通道-->
<TITLE GROUP_ROW="1" GROUP_COLUMN="1" GROUP_ROW_SPAN="4" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="3" GROUP_NAME="数字量通道" >DI_STATE</TITLE>   	                                                                                  
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="051" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="0" WIDGET_NAME="输入1" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="052" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="1" WIDGET_NAME="输入2(启动)" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="053" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="2" WIDGET_NAME="输入3" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="054" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="3" WIDGET_NAME="输入4" />

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="055" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="4" WIDGET_NAME="输出1" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="056" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="5" WIDGET_NAME="输出2" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="057" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="6" WIDGET_NAME="输出3" />	
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="058" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="6" WIDGET_NAME="地址码1" />

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="059" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="7" WIDGET_NAME="输出1保护" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="060" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="0" WIDGET_NAME="输出2保护" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="061" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="1" WIDGET_NAME="输出3保护" />	
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="062" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="7" WIDGET_NAME="地址码2" />

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="063" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="2" WIDGET_NAME="IGBT短路保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="064" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="1" WIDGET_NAME="PFC短路保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="065" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="0" WIDGET_NAME="DCDC短路保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="066" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="3" WIDGET_NAME="母线过压保护"/>

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="067" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="7" WIDGET_NAME="输入电流正半波保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="068" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="6" WIDGET_NAME="输入电流负半波保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="069" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="5" WIDGET_NAME="输出电流正半波保护"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="070" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="124" BYTE="00" BIT="4" WIDGET_NAME="输出电流负半波保护"/>

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="071" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="5" WIDGET_NAME="F-HD 故障刹车"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="072" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="3" WIDGET_NAME="CLR-HD 故障清除"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="073" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="4" WIDGET_NAME="FS-DR 使能反馈"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="074" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="2" WIDGET_NAME="POW"/>

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="075" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="6" WIDGET_NAME="散热风扇1故障"/>
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="076" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="123" BYTE="00" BIT="7" WIDGET_NAME="散热风扇2故障"/>	
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="077" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="2" WIDGET_NAME="FS-DR 使能输出"/>	

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="078" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="3" WIDGET_NAME="KMON1-直流主继电器" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="079" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="4" WIDGET_NAME="KMON2-母线主继电器" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="080" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="00" BIT="5" WIDGET_NAME="KMON3-未使用" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="081" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="00" BIT="7" WIDGET_NAME="直流升压使能" />

    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="082" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="0" BIT="0" WIDGET_NAME="CAN外网通讯状态" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="083" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="0" BIT="1" WIDGET_NAME="485外网通讯状态" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="084" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="0" BIT="2" WIDGET_NAME="WiFi串口通信状态" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="085" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="121" BYTE="0" BIT="5" WIDGET_NAME="WiFi网络通信状态" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="086" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="072" BYTE="0" BIT="2" WIDGET_NAME="逆变内网通讯状态" />
    <CHILD WIDGET_TYPE="IO" COLUMN_INDEX="087" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="072" BYTE="0" BIT="3" WIDGET_NAME="PFC内网通讯状态" />



<!--下列几组使用的偏移为多帧合并后,整帧数据中的偏移  -- >	

<!--附加数据 设备ID为手动追加   序列号为0x22帧     RETEXT_READ_OFFSETT使用的0x81帧   -->   
<TITLE GROUP_ROW="4" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="产品信息" >EXTRA_01</TITLE>                                
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="088" COLUMN_WIDTH="150" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="5" READ_OFFSET="228" BYTE="32" BIT="0" WIDGET_NAME="整机序列号" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="089" COLUMN_WIDTH="150" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="5" READ_OFFSET="260" BYTE="32" BIT="0" WIDGET_NAME="PCBA序列号" />                                                	                                                                                           
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="000" COLUMN_WIDTH="250" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="213" BYTE="04" BIT="0" WIDGET_NAME="RTC" READ_SCRIPT="通用_时间戳_秒.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="001" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="390" BYTE="01" BIT="0" WIDGET_NAME="设备ID"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="090" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="051" BYTE="02" BIT="0" WIDGET_NAME="软件版本" 			READ_SCRIPT="通用_主版本.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="091" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="051" BYTE="02" BIT="0" WIDGET_NAME="修订" 				READ_SCRIPT="通用_fix.js" />																								           
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="092" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="067" BYTE="02" BIT="0" WIDGET_NAME="硬件版本" 			READ_SCRIPT="通用_主版本.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="093" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="157" BYTE="01" BIT="0" WIDGET_NAME="型号" 				READ_SCRIPT="通用_派生型号.js"/>

<!-- 
自定义数据下发
WRITE_OFFSET为0x81帧的写入偏移,跟上面的回读偏移无关联
--> 
<TITLE GROUP_ROW="5" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="" >PUT_PARAMS</TITLE>
    <CHILD WIDGET_TYPE="SWITCH" WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="13" BYTE="1" BIT="0" WIDGET_NAME="控制方式" SWITCH_ON_TEXT="PTU控制" SWITCH_OFF_TEXT="DI控制" WRITE_SCRIPT="VFD_控制方式.js"/>
    <CHILD WIDGET_TYPE="SWITCH" WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="0" WIDGET_NAME="控制命令" SWITCH_ON_TEXT="启机" SWITCH_OFF_TEXT="停机" />
    <CHILD WIDGET_TYPE="PUT_PARAM" WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="24" BYTE="2" BIT="0" WIDGET_NAME="输出电压(V)" DEFAULT_VALUE="110" WIDGET_REGEXP="0|[1-9][0-9]*" />

</IO>
