<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 该节点的名称不可修改,固定为: VFD_FAULT -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="0" COLUMN_STRETCH="0" GROUP_NAME="故障" >VFD_FAULT</TITLE> 

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="04" BIT="0" WIDGET_NAME="条目序号" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="150" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="013" BYTE="08" BIT="0" WIDGET_NAME="时间" READ_SCRIPT="通用_时间_年月日时分秒.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="01" BIT="0" WIDGET_NAME="事件码" READ_SCRIPT="通用_日志事件码解析.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="250" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="020" BYTE="02" BIT="0" WIDGET_NAME="故障码" READ_SCRIPT="通用_日志诊断码解析.js" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="023" BYTE="01" BIT="0" WIDGET_NAME="系统状态机" 	READ_SCRIPT="通用_系统状态.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="024" BYTE="01" BIT="0" WIDGET_NAME="PFC状态机" 	READ_SCRIPT="通用_PFC升压状态.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="025" BYTE="01" BIT="0" WIDGET_NAME="输出状态机" 	READ_SCRIPT="通用_逆变状态.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="026" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 	READ_SCRIPT="通用_秒转分.js" />	

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="042" BYTE="02" BIT="0" WIDGET_NAME="R相输入电压"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="044" BYTE="02" BIT="0" WIDGET_NAME="S相输入电压"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="046" BYTE="02" BIT="0" WIDGET_NAME="T相输入电压"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="048" BYTE="02" BIT="0" WIDGET_NAME="R相输入电流" READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="050" BYTE="02" BIT="0" WIDGET_NAME="S相输入电流" READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="052" BYTE="02" BIT="0" WIDGET_NAME="T相输入电流" READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="054" BYTE="02" BIT="0" WIDGET_NAME="母线电压-inv"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="056" BYTE="02" BIT="0" WIDGET_NAME="输入频率(Hz)" 	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="058" BYTE="02" BIT="0" WIDGET_NAME="输入容量" 	READ_SCRIPT="通用_除100_小数2.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="060" BYTE="04" BIT="0" WIDGET_NAME="交流当前能耗"READ_SCRIPT="通用_除1000_小数3.js"/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="064" BYTE="02" BIT="0" WIDGET_NAME="输出原边电流" 	READ_SCRIPT="通用_除10_小数1.js" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="066" BYTE="02" BIT="0" WIDGET_NAME="母线电压-pfc"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="068" BYTE="02" BIT="0" WIDGET_NAME="输出频率(kHz)" 	READ_SCRIPT="通用_除100_小数2.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="070" BYTE="02" BIT="0" WIDGET_NAME="TIM占空比" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="072" BYTE="02" BIT="0" WIDGET_NAME="前_输出电压" 	READ_SCRIPT="通用_除10_小数1.js"  />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="074" BYTE="02" BIT="0" WIDGET_NAME="输出电压"  	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="078" BYTE="02" BIT="0" WIDGET_NAME="输出功率" 	READ_SCRIPT="通用_除100_小数2.js"/> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="080" BYTE="02" BIT="0" WIDGET_NAME="输出总电流" 	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="082" BYTE="02" BIT="0" WIDGET_NAME="充电电流"	READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="084" BYTE="04" BIT="0" WIDGET_NAME="直流当前能耗"	READ_SCRIPT="通用_除1000_小数3.js"/>


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="030" BYTE="02" BIT="0" WIDGET_NAME="CPU温度"/>
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="032" BYTE="02" BIT="0" WIDGET_NAME="12V电压"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="034" BYTE="02" BIT="0" WIDGET_NAME="5V电压"   READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="036" BYTE="02" BIT="0" WIDGET_NAME="3.3V电压" READ_SCRIPT="通用_除10_小数1.js"/>                                       
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="038" BYTE="02" BIT="0" WIDGET_NAME="交流额定功率" 	READ_SCRIPT="通用_除100_小数2.js"/>  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="040" BYTE="02" BIT="0" WIDGET_NAME="直流额定功率" 	READ_SCRIPT="通用_除100_小数2.js"/>  
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="076" BYTE="02" BIT="0" WIDGET_NAME="目标电压"/>

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="088" BYTE="02" BIT="0" WIDGET_NAME="共模电感温度"    READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="090" BYTE="02" BIT="0" WIDGET_NAME="PFC模块温度"     READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="092" BYTE="02" BIT="0" WIDGET_NAME="PFC电感温度"     READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="094" BYTE="02" BIT="0" WIDGET_NAME="母线电容温度"    READ_SCRIPT="通用_除10_小数1.js" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="096" BYTE="02" BIT="0" WIDGET_NAME="DC/DC模块温度"   READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="098" BYTE="02" BIT="0" WIDGET_NAME="变压器温度" 	     READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="100" BYTE="02" BIT="0" WIDGET_NAME="整流二极管温度"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="102" BYTE="02" BIT="0" WIDGET_NAME="外接电池温度"    READ_SCRIPT="通用_除10_小数1.js" /> 	

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="0" WIDGET_NAME="输入1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="1" WIDGET_NAME="输入2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="2" WIDGET_NAME="输入3" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="3" WIDGET_NAME="输出1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="4" WIDGET_NAME="输出2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="5" WIDGET_NAME="输出3" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="6" WIDGET_NAME="FO1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="104" BYTE="00" BIT="7" WIDGET_NAME="FO2" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="0" WIDGET_NAME="FO3" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="1" WIDGET_NAME="KMON1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="2" WIDGET_NAME="KMON2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="3" WIDGET_NAME="POW" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="4" WIDGET_NAME="CLR_HD" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="5" WIDGET_NAME="F_HD" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="6" WIDGET_NAME="FS_DR" /> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="105" BYTE="00" BIT="7" WIDGET_NAME="FS_DR_B" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="0" WIDGET_NAME="Addr1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="1" WIDGET_NAME="Addr2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="2" WIDGET_NAME="ICP_P" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="3" WIDGET_NAME="ICP_N" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="4" WIDGET_NAME="OCP_P" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="5" WIDGET_NAME="输入4" /> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="6" WIDGET_NAME="OCP_N" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="106" BYTE="00" BIT="7" WIDGET_NAME="KMON3" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="0" WIDGET_NAME="OVP_P_BUS" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="1" WIDGET_NAME="F_IPM_INV" /> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="2" WIDGET_NAME="F_IPM_PFC" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="3" WIDGET_NAME="F_IPM_DC" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="4" WIDGET_NAME="FAN1_F" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="107" BYTE="00" BIT="5" WIDGET_NAME="FAN2_F" />	


    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="0" WIDGET_NAME="hard_stop" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="1" WIDGET_NAME="soft_stop" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="2" WIDGET_NAME="diag_lvl_1" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="3" WIDGET_NAME="diag_lvl_2" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="4" WIDGET_NAME="diag_lvl_3" />	
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="5" WIDGET_NAME="diag_soft" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="111" BYTE="00" BIT="6" WIDGET_NAME="diag_hard" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="112" BYTE="00" BIT="4" WIDGET_NAME="com_485" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="112" BYTE="00" BIT="5" WIDGET_NAME="com_can" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="112" BYTE="00" BIT="6" WIDGET_NAME="com_ptu" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="113" BYTE="00" BIT="1" WIDGET_NAME="nvs_datas_init" />
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="113" BYTE="00" BIT="2" WIDGET_NAME="mount_fs" />

    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="116" BYTE="02" BIT="0" WIDGET_NAME="软件版本号" /> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="118" BYTE="02" BIT="0" WIDGET_NAME="硬件版本" /> 
    <CHILD WIDGET_TYPE="LOG_TEXT" WIDGET_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="120" BYTE="01" BIT="0" WIDGET_NAME="产品型号" /> 


</IO>                               
















