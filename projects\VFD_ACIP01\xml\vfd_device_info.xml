<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- RTC在合并数组中的偏移, 各项目可能不同  -->
<RTC READ_OFFSET="021" />

<!--系统状态   OPERATING_TEXT   -->	                                                                                                                                                  																						           
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="系统状态" >CONTROLLER_SYSTEM_INFO</TITLE>     
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="003" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="009" BYTE="04" BIT="0" WIDGET_NAME="当前上电时间" 		READ_SCRIPT="通用_秒转分.js" />	
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="004" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="013" BYTE="04" BIT="0" WIDGET_NAME="累计上电时间"   	READ_SCRIPT="通用_秒转分.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="005" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="017" BYTE="04" BIT="0" WIDGET_NAME="累计运行时间" 		READ_SCRIPT="通用_秒转分.js" />		                                                                                                  
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="000" COLUMN_WIDTH="250" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="021" BYTE="04" BIT="0" WIDGET_NAME="RTC" 		READ_SCRIPT="通用_时间戳_秒.js"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="006" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="025" BYTE="02" BIT="0" WIDGET_NAME="软件版本" 			READ_SCRIPT="通用_主版本.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="007" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="025" BYTE="02" BIT="0" WIDGET_NAME="修订" 				READ_SCRIPT="通用_fix.js" />																								           
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="008" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="027" BYTE="02" BIT="0" WIDGET_NAME="硬件版本" 			READ_SCRIPT="通用_主版本.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="009" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="029" BYTE="01" BIT="0" WIDGET_NAME="型号" 				READ_SCRIPT="通用_派生型号.js"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="002" COLUMN_WIDTH="060" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="030" BYTE="01" BIT="0" WIDGET_NAME="系统状态" 			READ_SCRIPT="通用_状态机.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="010" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="031" BYTE="01" BIT="0" WIDGET_NAME="LLC状态" 			READ_SCRIPT="通用_状态机.js"/>                                                                                             
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="011" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="034" BYTE="02" BIT="0" WIDGET_NAME="额定功率(kW)" 	    READ_SCRIPT="通用_除100_小数2.js"/> 

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="001" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="036" BYTE="01" BIT="0" WIDGET_NAME="设备ID"			READ_SCRIPT="通用_HEX.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="012" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="038" BYTE="02" BIT="0" WIDGET_NAME="MCU温度(℃)" 	    READ_SCRIPT="通用_除10_小数1.js" />   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="013" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="040" BYTE="02" BIT="0" WIDGET_NAME="3.3V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>

    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="014" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="042" BYTE="02" BIT="0" WIDGET_NAME="5V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="015" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="044" BYTE="02" BIT="0" WIDGET_NAME="12V电压(V)" 		READ_SCRIPT="通用_除10_小数1.js"/>                                                                                                     

<!-- 实时故障-->
<TITLE GROUP_ROW="0" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="0" GROUP_NAME="实时故障(右击清除记录)" >REALTIME_PFC_FAULT</TITLE>           
    <CHILD WIDGET_TYPE="FAULT" COLUMN_INDEX="016" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="3" READ_OFFSET="048" BYTE="12" BIT="0" WIDGET_NAME="系统故障字" 		READ_SCRIPT="通用_故障字.js"/>

<!-- PFC信息-->
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="模拟量" >POWER_INFO</TITLE>                                                                                 
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="017" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="068" BYTE="02" BIT="0" WIDGET_NAME="输入电压(V)"   	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="018" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="074" BYTE="02" BIT="0" WIDGET_NAME="输入预充电电压(V)"   	READ_SCRIPT="通用_除10_小数1.js"/>		
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="019" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="076" BYTE="02" BIT="0" WIDGET_NAME="LLC上电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="020" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="078" BYTE="02" BIT="0" WIDGET_NAME="LLC中电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="021" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="080" BYTE="02" BIT="0" WIDGET_NAME="LLC下电压(V)"		READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="022" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="070" BYTE="02" BIT="0" WIDGET_NAME="输出电压(V)" 	    READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="023" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="082" BYTE="02" BIT="0" WIDGET_NAME="输入电流(A)" 		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="024" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="072" BYTE="02" BIT="0" WIDGET_NAME="输出电流(A)" 		READ_SCRIPT="通用_除10_小数1.js" />

<!-- 温度信息-->
<TITLE GROUP_ROW="2" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="温度检测" >TEMP_INFO</TITLE>   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="025" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="104" BYTE="02" BIT="0" WIDGET_NAME="输入母线电容(℃)"  	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="026" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="100" BYTE="02" BIT="0" WIDGET_NAME="LLC1&2模块(℃)"    		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="027" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="106" BYTE="02" BIT="0" WIDGET_NAME="LLC2&3模块(℃)"    		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="028" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="102" BYTE="02" BIT="0" WIDGET_NAME="LLC变压器(℃)"   		READ_SCRIPT="通用_除10_小数1.js" /> 
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="029" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="098" BYTE="02" BIT="0" WIDGET_NAME="LLC谐振电感(℃)"		READ_SCRIPT="通用_除10_小数1.js" /> 
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="030" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="112" BYTE="02" BIT="0" WIDGET_NAME="LLC谐振电容(℃)"   		READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="031" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="108" BYTE="02" BIT="0" WIDGET_NAME="LLC整流1&2二极管(℃)"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="032" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="110" BYTE="02" BIT="0" WIDGET_NAME="LLC整流2&3二极管(℃)"  READ_SCRIPT="通用_除10_小数1.js" /> 						               											               
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="033" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="096" BYTE="02" BIT="0" WIDGET_NAME="输出母线电容(℃)"   	READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="034" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="092" BYTE="02" BIT="0" WIDGET_NAME="控制板温度(℃)" 		READ_SCRIPT="通用_除10_小数1.js" />   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="035" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="094" BYTE="02" BIT="0" WIDGET_NAME="控制板湿度(%)"  		READ_SCRIPT="通用_除10_小数1.js" />   

<!--  -->
<TITLE GROUP_ROW="1" GROUP_COLUMN="1" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="0" COLUMN_STRETCH="1" COLUMN_COUNT="3" GROUP_NAME="" >IO_LCD</TITLE>   	                                                                                  
    <CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="036" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="070" BYTE="02" BIT="0" WIDGET_NAME="输出电压(V)" 	DEFAULT_STR="000" READ_SCRIPT="通用_除10_小数1.js"/>
    <CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="037" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="072" BYTE="02" BIT="0" WIDGET_NAME="输出电流(A)" 	DEFAULT_STR="000"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="LCD6" COLUMN_INDEX="038" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="147" BYTE="04" BIT="0" WIDGET_NAME="输出功率(kW)" 	DEFAULT_STR="000"  READ_SCRIPT="通用_除1000_小数1.js" />

<!-- 数字量通道-->
<TITLE GROUP_ROW="2" GROUP_COLUMN="1" GROUP_ROW_SPAN="2" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="3" GROUP_NAME="数字量通道" >DI_STATE</TITLE>   	 

    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="039" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="0" WIDGET_NAME="输入1" />
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="040" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="1" WIDGET_NAME="输入2" />

    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="041" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="4" WIDGET_NAME="输出1" />
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="042" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="5" WIDGET_NAME="输出2" />
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="043" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="6" WIDGET_NAME="输出3" />	
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="044" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="137" BYTE="00" BIT="7" WIDGET_NAME="地址码1" />

    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="045" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="138" BYTE="00" BIT="0" WIDGET_NAME="地址码2" />						               											                                                       

    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="046" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="138" BYTE="00" BIT="5" WIDGET_NAME="F-IPM-LLC"/>
    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="047" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="138" BYTE="00" BIT="6" WIDGET_NAME="LLC_OCP_OUT"/>
    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="048" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="1" WIDGET_NAME="LLC_OVP_UPV"/>
    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="049" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="2" WIDGET_NAME="LLC_OVP_MIV"/>
    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="050" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="3" WIDGET_NAME="LLC_OVP_DNV"/>
    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="051" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="2" WIDGET_NAME="OVP_P_VBout"/>   

    <CHILD WIDGET_TYPE="IO_RE" 	COLUMN_INDEX="052" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="4" WIDGET_NAME="F-HD 故障刹车"/>
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="053" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="5" WIDGET_NAME="KMON1-主接触器" />
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="054" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="139" BYTE="00" BIT="6" WIDGET_NAME="KMON2-预充电" />

    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="055" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="0" WIDGET_NAME="FS-DR 使能输出"/>	
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="056" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="1" WIDGET_NAME="CLR-HD 故障清除"/>


    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="057" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="4" WIDGET_NAME="FS-DR 使能反馈"/>
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="058" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="5" WIDGET_NAME="POW"/>								               											                                                     
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="059" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="6" WIDGET_NAME="Flash挂载"/>	
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="060" COLUMN_WIDTH="100" WIDGET_SHOW="0" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="140" BYTE="00" BIT="7" WIDGET_NAME=" "/>	

    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="061" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="141" BYTE="00" BIT="0" WIDGET_NAME="CAN通讯" />
    <CHILD WIDGET_TYPE="IO" 	COLUMN_INDEX="062" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="141" BYTE="00" BIT="1" WIDGET_NAME="485通讯" />

<!--下列几组使用的偏移为多帧合并后,整帧数据中的偏移  -- >	

<!--附加数据 设备ID为手动追加   序列号为0x22帧    READ_OFFSETT使用的0x81帧   -->   
<TITLE GROUP_ROW="3" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="产品信息" >EXTRA_01</TITLE>                                
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="063" COLUMN_WIDTH="150" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="5" READ_OFFSET="180" BYTE="32" BIT="0" WIDGET_NAME="整机序列号" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="064" COLUMN_WIDTH="150" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="5" READ_OFFSET="212" BYTE="32" BIT="0" WIDGET_NAME="控制板序列号" />                                                	                                                                                           
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="065" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="143" BYTE="04" BIT="0" WIDGET_NAME="输入功率(kW)" READ_SCRIPT="通用_除1000_小数1.js" />   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="066" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="2" READ_OFFSET="147" BYTE="04" BIT="0" WIDGET_NAME="输出功率(kW)" READ_SCRIPT="通用_除1000_小数1.js" />   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="067" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="151" BYTE="04" BIT="0" WIDGET_NAME="LLC输出频率(kHz)"  />     
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="068" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="155" BYTE="02" BIT="0" WIDGET_NAME="LLC占空比(%)"  READ_SCRIPT="通用_除100_小数2.js"/>  
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="069" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="159" BYTE="01" BIT="0" WIDGET_NAME="LLC限流" 		READ_SCRIPT="通用_限流.js"/>   
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="070" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="084" BYTE="02" BIT="0" WIDGET_NAME="LLC设定电压(V)"  />     
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="071" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="086" BYTE="02" BIT="0" WIDGET_NAME="LLC下垂电压(V)"  />  
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="072" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="088" BYTE="02" BIT="0" WIDGET_NAME="均流度(%)"  READ_SCRIPT="通用_除100_小数2.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="073" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="118" BYTE="04" BIT="0" WIDGET_NAME="当前能耗(kWh)"  READ_SCRIPT="通用_除10_小数1.js" />
    <CHILD WIDGET_TYPE="TEXT" COLUMN_INDEX="074" COLUMN_WIDTH="100" WIDGET_SHOW="1" WIDGET_EDIT="0" WIDGET_DISP="1" READ_OFFSET="122" BYTE="04" BIT="0" WIDGET_NAME="累计能耗(kWh)"  READ_SCRIPT="通用_除10_小数1.js" />
<!-- 
自定义数据下发
WRITE_OFFSET为0x81帧的写入偏移,跟上面的回读偏移无关联
--> 
<TITLE GROUP_ROW="4" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="2" ROW_STRETCH="1" COLUMN_STRETCH="1" GROUP_NAME="" >PUT_PARAMS</TITLE>

    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="24" BYTE="2" BIT="0" WIDGET_NAME="LLC输出电压(V)" 		DEFAULT_VALUE="650" 	 WIDGET_REGEXP="0|[1-9][0-9]*" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="26" BYTE="2" BIT="0" WIDGET_NAME="LLC输出电流(A)" 		DEFAULT_VALUE="36.0" 	 WRITE_SCRIPT="通用_乘10.js" />
    <CHILD WIDGET_TYPE="PUT_PARAM" 	WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="28" BYTE="2" BIT="0" WIDGET_NAME="LLC下垂阻值(Ω)" 		DEFAULT_VALUE="1.0" 	 WRITE_SCRIPT="通用_乘10.js" />

    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="13" BYTE="1" BIT="0" WIDGET_NAME="控制方式" 		SWITCH_ON_TEXT="PTU控制" SWITCH_OFF_TEXT="DI控制" WRITE_SCRIPT="VFD_控制方式.js"/>
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="启机" 	 SWITCH_OFF_TEXT="停机" />

    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="2" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能DIO"  SWITCH_OFF_TEXT="使能DIO" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="3" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="O1" 	 SWITCH_OFF_TEXT="O1" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="4" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="O2" 	 SWITCH_OFF_TEXT="O2" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="2" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="5" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="O3" 	 SWITCH_OFF_TEXT="O3" />

    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="6" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="KMON1" 	 SWITCH_OFF_TEXT="KMON1" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="19" BYTE="0" BIT="7" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="KMON2" 	 SWITCH_OFF_TEXT="KMON2" />

    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="0" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能PWM"  SWITCH_OFF_TEXT="使能PWM" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="4" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="1" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="HRTim"  SWITCH_OFF_TEXT="HRTim" />

    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="5" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="2" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="使能"  SWITCH_OFF_TEXT="使能" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="5" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="3" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="电流前馈"  SWITCH_OFF_TEXT="电流前馈" />
    <CHILD WIDGET_TYPE="SWITCH" 	WIDGET_ROW="5" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_SHOW="1" WIDGET_DISP="1" WIDGET_EDIT="1" WRITE_OFFSET="20" BYTE="0" BIT="4" WIDGET_NAME="控制命令" 		SWITCH_ON_TEXT="CAN同步电压"  SWITCH_OFF_TEXT="CAN同步电压" />


</IO>