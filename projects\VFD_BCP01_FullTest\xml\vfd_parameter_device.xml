<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!-- 参数设置 -->
<TITLE GROUP_ROW="0" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="参数设置" >PVPB_DEVICE_01</TITLE>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="121" WRITE_OFFSET="121" BYTE="1" BIT="0" WIDGET_NAME="CAN_X通讯地址" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="122" WRITE_OFFSET="122" BYTE="1" BIT="0" WIDGET_NAME="CAN_Y通讯地址" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="3" READ_OFFSET="180" WRITE_OFFSET="180" BYTE="1" BIT="0" WIDGET_NAME="通信ID设置" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="115" WRITE_OFFSET="115" BYTE="2" BIT="0" WIDGET_NAME="控制板硬件版本" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="2" READ_OFFSET="185" WRITE_OFFSET="185" BYTE="2" BIT="0" WIDGET_NAME="PFC工作电压(V)" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="2" READ_OFFSET="175" WRITE_OFFSET="175" BYTE="4" BIT="0" WIDGET_NAME="Can波特率(bps)" READ_SCRIPT="通用_解析_CAN波特率.js" WRITE_SCRIPT="通用_设置_CAN波特率.js" ENUM_SCRIPT="通用_枚举_CAN波特率.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="171" WRITE_OFFSET="171" BYTE="4" BIT="0" WIDGET_NAME="RS485波特率(bps)" READ_SCRIPT="通用_解析_波特率.js" WRITE_SCRIPT="通用_设置_波特率.js" ENUM_SCRIPT="通用_枚举_波特率.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="ENUM_PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="1" WIDGET_DISP="1" READ_OFFSET="179" WRITE_OFFSET="179" BYTE="1" BIT="0" WIDGET_NAME="RS485校验位" READ_SCRIPT="通用_解析_奇偶校验.js" WRITE_SCRIPT="通用_设置_奇偶校验.js" ENUM_SCRIPT="通用_枚举_奇偶校验.js"/>																						 		 		  				  					      	 


<!-- 运营统计 -->                                                                                                                                                                  																						 		 		  				  					      	 
<TITLE GROUP_ROW="1" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="运营统计" >PVPB_DEVICE_02</TITLE> 
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="181" WRITE_OFFSET="181" BYTE="1" BIT="0" WIDGET_NAME="清除全部统计数据" />
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="203" WRITE_OFFSET="203" BYTE="4" BIT="0" WIDGET_NAME="累计总时长(s)" />
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="215" WRITE_OFFSET="215" BYTE="4" BIT="0" WIDGET_NAME="累计运行时长(s)" />	
    <CHILD WIDGET_ROW="00" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="182" WRITE_OFFSET="182" BYTE="1" BIT="0" WIDGET_NAME="运行率(s)" READ_SCRIPT="通用_除100_小数2.js"/>	

    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="009" WRITE_OFFSET="009" BYTE="4" BIT="0" WIDGET_NAME="输入功率_600W" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="013" WRITE_OFFSET="013" BYTE="4" BIT="0" WIDGET_NAME="输入功率6-700W" />
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="017" WRITE_OFFSET="017" BYTE="4" BIT="0" WIDGET_NAME="输入功率7-800W" />																		 		 		  				  					      	 
    <CHILD WIDGET_ROW="01" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="021" WRITE_OFFSET="021" BYTE="4" BIT="0" WIDGET_NAME="输入功率8-900W" />

    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="025" WRITE_OFFSET="025" BYTE="4" BIT="0" WIDGET_NAME="输入功率9-1000W" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="029" WRITE_OFFSET="029" BYTE="4" BIT="0" WIDGET_NAME="输入功率10-1100W" />
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="033" WRITE_OFFSET="033" BYTE="4" BIT="0" WIDGET_NAME="输入功率11-1200W" />																				 		 		  				  					      	 
    <CHILD WIDGET_ROW="02" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="037" WRITE_OFFSET="037" BYTE="4" BIT="0" WIDGET_NAME="输入功率1200W_" />

    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="041" WRITE_OFFSET="041" BYTE="4" BIT="0" WIDGET_NAME="输出频率_35Hz" />
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="045" WRITE_OFFSET="045" BYTE="4" BIT="0" WIDGET_NAME="输出频率35_40Hz" />
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="049" WRITE_OFFSET="049" BYTE="4" BIT="0" WIDGET_NAME="输出频率40_45Hz" />													 		 		  				  			               
    <CHILD WIDGET_ROW="03" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="053" WRITE_OFFSET="053" BYTE="4" BIT="0" WIDGET_NAME="输出频率45_50Hz" />
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="057" WRITE_OFFSET="057" BYTE="4" BIT="0" WIDGET_NAME="输出频率50_55Hz" />
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="061" WRITE_OFFSET="061" BYTE="4" BIT="0" WIDGET_NAME="输出频率55_60Hz" />
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="065" WRITE_OFFSET="065" BYTE="4" BIT="0" WIDGET_NAME="输出频率60_65Hz" />
    <CHILD WIDGET_ROW="04" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="069" WRITE_OFFSET="069" BYTE="4" BIT="0" WIDGET_NAME="输出频率65_Hz" />

    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="073" WRITE_OFFSET="073" BYTE="4" BIT="0" WIDGET_NAME="MCU温度_40" />
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="077" WRITE_OFFSET="077" BYTE="4" BIT="0" WIDGET_NAME="MCU温度40_50" />
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="081" WRITE_OFFSET="081" BYTE="4" BIT="0" WIDGET_NAME="MCU温度50_60" />
    <CHILD WIDGET_ROW="05" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="085" WRITE_OFFSET="085" BYTE="4" BIT="0" WIDGET_NAME="MCU温度60_70" />
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="089" WRITE_OFFSET="089" BYTE="4" BIT="0" WIDGET_NAME="MCU温度70_80" />
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="093" WRITE_OFFSET="093" BYTE="4" BIT="0" WIDGET_NAME="MCU温度80_90" />
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="235" WRITE_OFFSET="235" BYTE="4" BIT="0" WIDGET_NAME="MCU温度90_100" />
    <CHILD WIDGET_ROW="06" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="239" WRITE_OFFSET="239" BYTE="4" BIT="0" WIDGET_NAME="MCU温度100_" />

    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="243" WRITE_OFFSET="243" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度_40" />
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="247" WRITE_OFFSET="247" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度40_50" />
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="251" WRITE_OFFSET="251" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度50_60" />
    <CHILD WIDGET_ROW="07" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="255" WRITE_OFFSET="255" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度60_70" />
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="259" WRITE_OFFSET="259" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度70_80" />
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="263" WRITE_OFFSET="263" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度80_90" />
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="267" WRITE_OFFSET="267" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度90_100" />
    <CHILD WIDGET_ROW="08" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="271" WRITE_OFFSET="271" BYTE="4" BIT="0" WIDGET_NAME="PFC电感温度100_" />

    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="275" WRITE_OFFSET="275" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度_40" />
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="279" WRITE_OFFSET="279" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度40_50" />
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="283" WRITE_OFFSET="283" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度50_60" />
    <CHILD WIDGET_ROW="09" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="287" WRITE_OFFSET="287" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度60_70" />
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="291" WRITE_OFFSET="291" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度70_80" />
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="295" WRITE_OFFSET="295" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度80_90" />
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="299" WRITE_OFFSET="299" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度90_100" />
    <CHILD WIDGET_ROW="10" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="303" WRITE_OFFSET="303" BYTE="4" BIT="0" WIDGET_NAME="逆变IGBT温度100_" />

    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="307" WRITE_OFFSET="307" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度_40" />
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="311" WRITE_OFFSET="311" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度40_50" />
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="315" WRITE_OFFSET="315" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度50_60" />
    <CHILD WIDGET_ROW="11" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="319" WRITE_OFFSET="319" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度60_70" />
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="323" WRITE_OFFSET="323" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度70_80" />
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="327" WRITE_OFFSET="327" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度80_90" />
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="331" WRITE_OFFSET="331" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度90_100" />
    <CHILD WIDGET_ROW="12" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="335" WRITE_OFFSET="335" BYTE="4" BIT="0" WIDGET_NAME="PFC-IGBT温度100_" />

    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="339" WRITE_OFFSET="339" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度_40" />
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="343" WRITE_OFFSET="343" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度40_50" />
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="347" WRITE_OFFSET="347" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度50_60" />
    <CHILD WIDGET_ROW="13" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="351" WRITE_OFFSET="351" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度60_70" />
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="355" WRITE_OFFSET="355" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度70_80" />
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="359" WRITE_OFFSET="359" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度80_90" />
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="363" WRITE_OFFSET="363" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度90_100" />
    <CHILD WIDGET_ROW="14" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="367" WRITE_OFFSET="367" BYTE="4" BIT="0" WIDGET_NAME="DCDC-IGBT温度100_" />

    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="371" WRITE_OFFSET="371" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度_40" />
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="375" WRITE_OFFSET="375" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度40_50" />
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="379" WRITE_OFFSET="379" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度50_60" />
    <CHILD WIDGET_ROW="15" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="383" WRITE_OFFSET="383" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度60_70" />
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="387" WRITE_OFFSET="387" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度70_80" />
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="391" WRITE_OFFSET="391" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度80_90" />
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="395" WRITE_OFFSET="395" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度90_100" />
    <CHILD WIDGET_ROW="16" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="399" WRITE_OFFSET="399" BYTE="4" BIT="0" WIDGET_NAME="母线电容温度100_" />

    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="403" WRITE_OFFSET="403" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度_40" />
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="407" WRITE_OFFSET="407" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度40_50" />
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="411" WRITE_OFFSET="411" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度50_60" />
    <CHILD WIDGET_ROW="17" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="415" WRITE_OFFSET="415" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度60_70" />
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="419" WRITE_OFFSET="419" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度70_80" />
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="423" WRITE_OFFSET="423" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度80_90" />
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="427" WRITE_OFFSET="427" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度90_100" />
    <CHILD WIDGET_ROW="18" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="431" WRITE_OFFSET="431" BYTE="4" BIT="0" WIDGET_NAME="共膜电感温度100_" />

    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="123" WRITE_OFFSET="123" BYTE="4" BIT="0" WIDGET_NAME="交流累计能耗(kWh)" READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js" />																						 		 		  				  					      	 
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="127" WRITE_OFFSET="127" BYTE="4" BIT="0" WIDGET_NAME="直流累计能耗(kWh)" READ_SCRIPT="通用_除1000_小数3.js" WRITE_SCRIPT="通用_乘1000.js" />
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="191" WRITE_OFFSET="191" BYTE="4" BIT="0" WIDGET_NAME="三相启停次数" />
    <CHILD WIDGET_ROW="19" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="195" WRITE_OFFSET="195" BYTE="4" BIT="0" WIDGET_NAME="主继电器吸合次数" />	

    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="199" WRITE_OFFSET="199" BYTE="4" BIT="0" WIDGET_NAME="主继电器断开次数" />
    <CHILD WIDGET_ROW="20" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="0" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="211" WRITE_OFFSET="211" BYTE="4" BIT="0" WIDGET_NAME="380V上电次数" />																						 		 		  				  					      	 

<!-- 测试参数 -->
<TITLE GROUP_ROW="2" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="测试参数" >PVPB_DEVICE_03</TITLE>																				 		 		  				  					      	 
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="131" WRITE_OFFSET="131" BYTE="1" BIT="0" WIDGET_NAME="清除所有强制" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="097" WRITE_OFFSET="097" BYTE="1" BIT="0" WIDGET_NAME="KMON1继电器控制" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="098" WRITE_OFFSET="098" BYTE="1" BIT="0" WIDGET_NAME="KMON2继电器控制" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="451" WRITE_OFFSET="451" BYTE="1" BIT="0" WIDGET_NAME="取消温度补偿控制" />

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="135" WRITE_OFFSET="135" BYTE="1" BIT="0" WIDGET_NAME="输入1强制" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="136" WRITE_OFFSET="136" BYTE="1" BIT="0" WIDGET_NAME="输入2强制" />	 
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="137" WRITE_OFFSET="137" BYTE="1" BIT="0" WIDGET_NAME="输入3强制" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="138" WRITE_OFFSET="138" BYTE="1" BIT="0" WIDGET_NAME="输入4强制" />

    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="132" WRITE_OFFSET="132" BYTE="1" BIT="0" WIDGET_NAME="输出1控制" />																					 		 		  				  					      	 
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="133" WRITE_OFFSET="133" BYTE="1" BIT="0" WIDGET_NAME="输出2控制" />
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="134" WRITE_OFFSET="134" BYTE="1" BIT="0" WIDGET_NAME="输出3控制" />
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="143" WRITE_OFFSET="143" BYTE="1" BIT="0" WIDGET_NAME="风扇驱动PWM强制(%)" />

    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="140" WRITE_OFFSET="140" BYTE="1" BIT="0" WIDGET_NAME="PFC模块U相PWM强制(%)" />
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="141" WRITE_OFFSET="141" BYTE="1" BIT="0" WIDGET_NAME="PFC模块V相PWM强制(%)" />																						 		 		  				  					      	 
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="142" WRITE_OFFSET="142" BYTE="1" BIT="0" WIDGET_NAME="PFC模块W相PWM强制(%)" />
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="144" WRITE_OFFSET="144" BYTE="1" BIT="0" WIDGET_NAME="LLC模块U相PWM强制(%)" />

    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="2" READ_OFFSET="145" WRITE_OFFSET="145" BYTE="1" BIT="0" WIDGET_NAME="LLC模块V相PWM强制(%)" />


<!-- 校准参数 -->
<TITLE GROUP_ROW="3" GROUP_COLUMN="0" GROUP_ROW_SPAN="1" GROUP_COLUMN_SPAN="1" ROW_STRETCH="1" COLUMN_STRETCH="1" COLUMN_COUNT="4" GROUP_NAME="校准参数" >PVPB_DEVICE_04</TITLE>																		 		 		  				  					      	 
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="147" WRITE_OFFSET="147" BYTE="2" BIT="0" WIDGET_NAME="输入电压R相(V)" />
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="153" WRITE_OFFSET="153" BYTE="2" BIT="0" WIDGET_NAME="输入电流R相(A)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="163" WRITE_OFFSET="163" BYTE="2" BIT="0" WIDGET_NAME="蓄电池电流校准(A)" 	READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="0" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="159" WRITE_OFFSET="159" BYTE="2" BIT="0" WIDGET_NAME="PFC模块母线电压(V)" />

    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="149" WRITE_OFFSET="149" BYTE="2" BIT="0" WIDGET_NAME="输入电压S相(V)" />
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="155" WRITE_OFFSET="155" BYTE="2" BIT="0" WIDGET_NAME="输入电流S相(A)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="165" WRITE_OFFSET="165" BYTE="2" BIT="0" WIDGET_NAME="总输出电流校准(A)" 	READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="1" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="161" WRITE_OFFSET="161" BYTE="2" BIT="0" WIDGET_NAME="INV模块母线电压(V)" />

    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="151" WRITE_OFFSET="151" BYTE="2" BIT="0" WIDGET_NAME="输入电压T相(V)" />		
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="157" WRITE_OFFSET="157" BYTE="2" BIT="0" WIDGET_NAME="输入电流T相(A)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="167" WRITE_OFFSET="167" BYTE="2" BIT="0" WIDGET_NAME="输出电压校准(V)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="2" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="169" WRITE_OFFSET="169" BYTE="2" BIT="0" WIDGET_NAME="前输出电压校准(V)" 		READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="435" WRITE_OFFSET="435" BYTE="2" BIT="0" WIDGET_NAME="共模电感温度校准(℃)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>   
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="437" WRITE_OFFSET="437" BYTE="2" BIT="0" WIDGET_NAME="PFC电感温度校准(℃)"  READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="439" WRITE_OFFSET="439" BYTE="2" BIT="0" WIDGET_NAME="PFC模块温度校准(℃)"  READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="3" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="441" WRITE_OFFSET="441" BYTE="2" BIT="0" WIDGET_NAME="母线电容温度校准(℃)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="0" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="443" WRITE_OFFSET="443" BYTE="2" BIT="0" WIDGET_NAME="DC/DC模块温度校准(℃)"  READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="1" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="445" WRITE_OFFSET="445" BYTE="2" BIT="0" WIDGET_NAME="变压器温度校准(℃)"     READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="2" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="447" WRITE_OFFSET="447" BYTE="2" BIT="0" WIDGET_NAME="整流二极管温度校准(℃)" READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>
    <CHILD WIDGET_ROW="4" WIDGET_COLUMN="3" WIDGET_ROW_SPAN="1" WIDGET_COLUMN_SPAN="1" WIDGET_TYPE="PARAM" WIDGET_SHOW="1" WIDGET_EDIT="1" DO_CRC="0" WIDGET_DISP="1" READ_OFFSET="449" WRITE_OFFSET="449" BYTE="2" BIT="0" WIDGET_NAME="外接电池温度校准(℃)"   READ_SCRIPT="通用_除10_小数1.js" WRITE_SCRIPT="通用_乘10.js"/>

</IO>
