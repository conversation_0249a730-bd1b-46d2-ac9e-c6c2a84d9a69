function myPadStart(str,targetLength, padString) 
{ 
    while (str.length < targetLength) 
	{
        str = padString + str;
    }
    return str;
}

function fun(val)
{
	var time = parseInt(val);
	var h = parseInt(time / 3600).toString();
    var m = parseInt(time / 60 % 60).toString();
    var s = Math.ceil(time % 60).toString();
	
	var hh = myPadStart(h,2,'0');
	var mm = myPadStart(m,2,'0');
	var ss = myPadStart(s,2,'0');
	
	return hh + ":" + mm + ":" + ss;
}
