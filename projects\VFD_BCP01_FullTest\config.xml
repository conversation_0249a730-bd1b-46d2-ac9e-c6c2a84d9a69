<?xml version="1.0" encoding="UTF-8"?>
<IO>
<!--软件配置 -->
<PTU_VERSION>V1.04-F20</PTU_VERSION>
<PTU_TITLE>PTU-BCP01型蓄电池充电机-</PTU_TITLE>

<!--串口配置 -->
<SERIAL_NAME>COM1</SERIAL_NAME>
<BAUDRATE>115200</BAUDRATE>
<DATABITS>8</DATABITS>
<STOPBITS>1</STOPBITS>
<PARITY>0</PARITY>
<DTR>0</DTR>
<RTS>0</RTS>
<!--网口配置 -->
<REMOTE_IP>*********|***********|***********</REMOTE_IP>
<REMOTE_PORT>50000</REMOTE_PORT>
<LOCAL_PORT>20181</LOCAL_PORT>
<!--校验算法-->
<SEND_CHECKSUM>CRC16_A001</SEND_CHECKSUM>
<RECV_CHECKSUM>CRC16_A001</RECV_CHECKSUM>
<!--发送周期-->
<SEND_INTERVAL>50</SEND_INTERVAL>
<!--工作模块-->
<VFD_DEVICE_WIDGET>1</VFD_DEVICE_WIDGET>
<VFD_PARAMETER_WIDGET>1</VFD_PARAMETER_WIDGET>
<VFD_UPDATE_WIDGET>1</VFD_UPDATE_WIDGET>
<VFD_FAULT_WIDGET>1</VFD_FAULT_WIDGET>
<VFD_PARSE_CACHE_WIDGET>1</VFD_PARSE_CACHE_WIDGET>
<VFD_MODBUS_WIDGET>0</VFD_MODBUS_WIDGET>
<!--项目专有信息-->
<PVPB_UPDATE_TIMEOUT>60</PVPB_UPDATE_TIMEOUT>   <!-- 更新固件最大超时时间（秒）-->
<!-- 自定义标签-->
<PVPB_CUSTOM_LABEL1 TEXT="输出电压(V)" DEFAULT_VALUE="110" WRITE_SCRIPT="" />
<!--默认检测干扰信号时间(毫秒)-->
<NOISE_INTERVAL>2000</NOISE_INTERVAL>
<!--默认保存通讯数据-->
<SAVE_COMMUNICATE_HEX>1</SAVE_COMMUNICATE_HEX>

<!-- 取固件版本的脚本-->
<GET_BIN_VER_SCRIPT>VFD_通用_固件版本.js</GET_BIN_VER_SCRIPT>
<!-- 取固件类型的脚本-->
<GET_BIN_TYPE_SCRIPT>VFD_通用_固件类型.js</GET_BIN_TYPE_SCRIPT>
<!-- 取通讯协议中,各类项目存储[软件版本]的偏移-->
<GET_BIN_VER_OFFSET>VFD_通用_固件更新_取版本偏移.js</GET_BIN_VER_OFFSET>

<PTU_SYS_SETTING>1</PTU_SYS_SETTING>
<PTU_FLASH_LOADER>1</PTU_FLASH_LOADER>
<PTU_CRT_WIDGET>1</PTU_CRT_WIDGET>
</IO>
